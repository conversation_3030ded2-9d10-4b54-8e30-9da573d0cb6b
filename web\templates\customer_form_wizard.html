{% extends "base.html" %}

{% block title %}Form Configuration Wizard - Google Form AutoFill{% endblock %}

{% block head %}
<style>
    body {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        min-height: 100vh;
        color: #ffffff;
    }

    .wizard-container {
        max-width: 800px;
        margin: 2rem auto;
        background: linear-gradient(145deg, #2a2a2a 0%, #1e1e1e 100%);
        border-radius: 15px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.4);
        overflow: hidden;
        animation: slideIn 0.6s ease-out;
        border: 1px solid #444;
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(50px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .wizard-header {
        background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
        color: white;
        padding: 2rem;
        text-align: center;
    }

    .wizard-header h1 {
        margin: 0;
        font-size: 2rem;
        font-weight: 300;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .wizard-header p {
        margin: 0.5rem 0 0;
        opacity: 0.9;
    }

    .progress-bar-container {
        background: rgba(255,255,255,0.2);
        height: 6px;
        border-radius: 3px;
        margin-top: 1.5rem;
        overflow: hidden;
    }

    .progress-bar {
        background: rgba(255,255,255,0.8);
        height: 100%;
        border-radius: 3px;
        width: 0%;
        transition: width 0.3s ease;
        box-shadow: 0 0 10px rgba(255,255,255,0.5);
    }

    .wizard-content {
        padding: 3rem;
        min-height: 400px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        background: #2a2a2a;
    }

    .step {
        display: none;
        animation: fadeIn 0.5s ease-in;
    }

    .step.active {
        display: block;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateX(20px); }
        to { opacity: 1; transform: translateX(0); }
    }

    .step-title {
        font-size: 1.8rem;
        color: #ffffff;
        margin-bottom: 1rem;
        text-align: center;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .step-description {
        color: #b0b0b0;
        text-align: center;
        margin-bottom: 2rem;
        font-size: 1.1rem;
    }

    .form-group {
        margin-bottom: 2rem;
    }

    .form-label {
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 0.8rem;
        display: block;
    }

    .form-control {
        border: 2px solid #444;
        border-radius: 10px;
        padding: 1rem;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        background: #333;
        color: #ffffff;
    }

    .form-control:focus {
        border-color: #6c5ce7;
        box-shadow: 0 0 0 0.2rem rgba(108, 92, 231, 0.25);
        background: #3a3a3a;
    }

    .form-control::placeholder {
        color: #888;
    }

    .form-text {
        color: #888;
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }

    .alert {
        background: rgba(116, 185, 255, 0.1);
        border: 1px solid rgba(116, 185, 255, 0.3);
        color: #74b9ff;
        border-radius: 10px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .option-card {
        border: 2px solid #444;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        cursor: pointer;
        background: #333;
    }

    .option-card:hover {
        border-color: #6c5ce7;
        background: #3a3a3a;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 92, 231, 0.2);
    }

    .option-card.selected {
        border-color: #6c5ce7;
        background: linear-gradient(135deg, #3a3a3a 0%, #444 100%);
    }

    .option-title {
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 0.5rem;
    }

    .option-description {
        color: #b0b0b0;
        font-size: 0.9rem;
    }

    .slider-container {
        margin: 1rem 0;
    }

    .slider-label {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .slider-option {
        font-weight: 500;
        color: #ffffff;
    }

    .slider-value {
        background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
        color: white;
        padding: 0.3rem 0.8rem;
        border-radius: 20px;
        font-weight: 600;
        min-width: 50px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(108, 92, 231, 0.3);
    }

    .range-slider {
        width: 100%;
        height: 8px;
        border-radius: 4px;
        background: #444;
        outline: none;
        margin: 0.5rem 0;
        -webkit-appearance: none;
    }

    .range-slider::-webkit-slider-thumb {
        -webkit-appearance: none;
        appearance: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
        cursor: pointer;
        box-shadow: 0 2px 6px rgba(108, 92, 231, 0.5);
    }

    .range-slider::-moz-range-thumb {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
        cursor: pointer;
        border: none;
        box-shadow: 0 2px 6px rgba(108, 92, 231, 0.5);
    }

    .total-percentage {
        background: #333;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
        margin-top: 1rem;
        font-weight: 600;
        border: 2px solid #444;
    }

    .total-percentage.valid {
        background: rgba(0, 184, 148, 0.2);
        border-color: #00b894;
        color: #00b894;
    }

    .total-percentage.invalid {
        background: rgba(225, 112, 85, 0.2);
        border-color: #e17055;
        color: #e17055;
    }

    .example-input {
        margin-bottom: 1rem;
    }

    .example-input textarea {
        min-height: 80px;
        resize: vertical;
    }

    .wizard-navigation {
        background: #1e1e1e;
        padding: 1.5rem 3rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-top: 1px solid #444;
    }

    .btn-wizard {
        padding: 0.8rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(108, 92, 231, 0.3);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(108, 92, 231, 0.4);
    }

    .btn-secondary {
        background: #444;
        color: white;
    }

    .btn-secondary:hover {
        background: #555;
        transform: translateY(-2px);
    }

    .btn-outline {
        background: transparent;
        border: 2px solid #666;
        color: #ccc;
    }

    .btn-outline:hover {
        background: #666;
        color: white;
    }

    .step-indicator {
        display: flex;
        justify-content: center;
        margin-bottom: 2rem;
    }

    .step-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #444;
        margin: 0 0.5rem;
        transition: all 0.3s ease;
    }

    .step-dot.active {
        background: #6c5ce7;
        transform: scale(1.2);
        box-shadow: 0 0 10px rgba(108, 92, 231, 0.5);
    }

    .step-dot.completed {
        background: #00b894;
    }

    .welcome-icon {
        font-size: 4rem;
        color: #6c5ce7;
        margin-bottom: 1rem;
        text-align: center;
        text-shadow: 0 0 20px rgba(108, 92, 231, 0.5);
    }

    .completion-icon {
        font-size: 4rem;
        color: #00b894;
        margin-bottom: 1rem;
        text-align: center;
        text-shadow: 0 0 20px rgba(0, 184, 148, 0.5);
    }

    .summary-card {
        background: linear-gradient(145deg, #333 0%, #2a2a2a 100%);
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        border: 1px solid #444;
        box-shadow: 0 4px 10px rgba(0,0,0,0.3);
    }

    .summary-title {
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 0.5rem;
    }

    .summary-content {
        color: #b0b0b0;
    }

    .step-counter {
        color: #b0b0b0;
        font-weight: 500;
    }

    .text-muted {
        color: #888 !important;
    }

    /* Navigation Menu Styles */
    .wizard-nav-menu {
        background: #333;
        border-top: 1px solid #444;
        border-bottom: 1px solid #444;
    }

    .nav-menu-header {
        padding: 1rem 3rem;
        background: #2a2a2a;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
        transition: all 0.3s ease;
        user-select: none;
    }

    .nav-menu-header:hover {
        background: #3a3a3a;
    }

    .nav-menu-header span {
        color: #ffffff;
        font-weight: 600;
        flex-grow: 1;
    }

    .nav-chevron {
        color: #b0b0b0;
        transition: transform 0.3s ease;
    }

    .nav-menu-header.open .nav-chevron {
        transform: rotate(180deg);
    }

    .nav-menu-content {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
        background: #2a2a2a;
    }

    .nav-menu-content.open {
        max-height: 400px;
        overflow-y: auto;
    }

    .nav-menu-item {
        padding: 1rem 3rem;
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
        border-bottom: 1px solid #444;
    }

    .nav-menu-item:hover {
        background: #3a3a3a;
        transform: translateX(5px);
    }

    .nav-menu-item.active {
        background: linear-gradient(135deg, rgba(108, 92, 231, 0.2) 0%, rgba(162, 155, 254, 0.2) 100%);
        border-left: 4px solid #6c5ce7;
    }

    .nav-menu-item.completed {
        background: rgba(0, 184, 148, 0.1);
        border-left: 4px solid #00b894;
    }

    .nav-item-icon {
        font-size: 1.5rem;
        margin-right: 1rem;
        width: 40px;
        text-align: center;
    }

    .nav-item-text {
        flex-grow: 1;
    }

    .nav-item-title {
        color: #ffffff;
        font-weight: 600;
        margin-bottom: 0.2rem;
    }

    .nav-item-subtitle {
        color: #b0b0b0;
        font-size: 0.9rem;
    }

    .nav-item-status {
        color: #666;
        font-size: 0.8rem;
    }

    .nav-menu-item.completed .nav-item-status {
        color: #00b894;
    }

    .nav-menu-item.active .nav-item-status {
        color: #6c5ce7;
    }

    @media (max-width: 768px) {
        .wizard-container {
            margin: 1rem;
            border-radius: 10px;
        }

        .wizard-content {
            padding: 2rem 1.5rem;
        }

        .wizard-navigation {
            padding: 1rem 1.5rem;
            flex-direction: column;
            gap: 1rem;
        }

        .btn-wizard {
            width: 100%;
            justify-content: center;
        }

        .nav-menu-header,
        .nav-menu-item {
            padding: 1rem 1.5rem;
        }

        .nav-menu-item.active,
        .nav-menu-item.completed {
            border-left: 3px solid;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="wizard-container">
    <!-- Header -->
    <div class="wizard-header">
        <h1><i class="fas fa-magic me-2"></i>Form Configuration Wizard</h1>
        <p>{{ form_data.form_title }}</p>
        <div class="progress-bar-container">
            <div class="progress-bar" id="progressBar"></div>
        </div>
    </div>

    <!-- Navigation Menu (Table of Contents) -->
    <div class="wizard-nav-menu" id="wizardNavMenu">
        <div class="nav-menu-header" onclick="toggleNavMenu()">
            <i class="fas fa-list me-2"></i>
            <span>Navigation Menu</span>
            <i class="fas fa-chevron-down nav-chevron" id="navChevron"></i>
        </div>
        <div class="nav-menu-content" id="navMenuContent">
            <div class="nav-menu-item active" data-step="0" onclick="navigateToStep(0)">
                <div class="nav-item-icon">🚀</div>
                <div class="nav-item-text">
                    <div class="nav-item-title">Welcome</div>
                    <div class="nav-item-subtitle">Get started</div>
                </div>
                <div class="nav-item-status">
                    <i class="fas fa-check"></i>
                </div>
            </div>
            <div class="nav-menu-item" data-step="1" onclick="navigateToStep(1)">
                <div class="nav-item-icon">🔢</div>
                <div class="nav-item-text">
                    <div class="nav-item-title">Total Responses</div>
                    <div class="nav-item-subtitle">How many responses</div>
                </div>
                <div class="nav-item-status">
                    <i class="fas fa-circle"></i>
                </div>
            </div>
            <!-- Dynamic question items will be added here by JavaScript -->
            <div class="nav-menu-item" data-step="final" onclick="navigateToStep('final')">
                <div class="nav-item-icon">✅</div>
                <div class="nav-item-text">
                    <div class="nav-item-title">Summary</div>
                    <div class="nav-item-subtitle">Review & save</div>
                </div>
                <div class="nav-item-status">
                    <i class="fas fa-circle"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Content -->
    <div class="wizard-content">
        <!-- Step Indicators -->
        <div class="step-indicator" id="stepIndicator">
            <!-- Will be populated by JavaScript -->
        </div>

        <!-- Step 1: Welcome -->
        <div class="step active" data-step="0">
            <div class="welcome-icon">
                <i class="fas fa-rocket"></i>
            </div>
            <h2 class="step-title">Welcome to the Configuration Wizard!</h2>
            <p class="step-description">
                We'll guide you through configuring your form responses step by step.
                This should take about 3-5 minutes.
            </p>
            <div class="summary-card">
                <div class="summary-title">Form Details</div>
                <div class="summary-content">
                    <strong>Title:</strong> {{ form_data.form_title }}<br>
                    {% if form_data.form_description %}
                    <strong>Description:</strong> {{ form_data.form_description }}<br>
                    {% endif %}
                    <strong>Questions:</strong> {{ form_data.questions|length }} questions to configure
                </div>
            </div>
        </div>

        <!-- Step 2: Total Responses -->
        <div class="step" data-step="1">
            <h2 class="step-title">How many responses do you need?</h2>
            <p class="step-description">
                Specify the total number of form submissions you want to generate.
            </p>
            <div class="form-group">
                <label class="form-label">Number of Responses</label>
                <input type="number" class="form-control" id="totalResponses"
                       value="10" min="1" max="10000" placeholder="Enter number of responses">
                <div class="form-text">Recommended: 10-100 responses for testing</div>
            </div>
        </div>

        <!-- Dynamic Question Steps -->
        <!-- Will be populated by JavaScript -->

        <!-- Final Step: Summary -->
        <div class="step" data-step="final">
            <div class="completion-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <h2 class="step-title">Configuration Complete!</h2>
            <p class="step-description">
                Review your configuration below. You can go back to make changes or save to proceed.
            </p>
            <div id="configurationSummary">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="wizard-navigation">
        <button type="button" class="btn-wizard btn-outline" id="prevBtn" style="display: none;">
            <i class="fas fa-arrow-left"></i> Previous
        </button>
        <div class="step-counter">
            <span id="currentStep">1</span> of <span id="totalSteps">1</span>
        </div>
        <button type="button" class="btn-wizard btn-primary" id="nextBtn">
            Next <i class="fas fa-arrow-right"></i>
        </button>
    </div>
</div>

<!-- Hidden form for submission -->
<form method="POST" id="wizardForm" style="display: none;">
    {{ form.csrf_token }}
    <input type="hidden" name="total_responses" id="hiddenTotalResponses">
    <div id="hiddenQuestionInputs"></div>
</form>
{% endblock %}

{% block scripts %}
<script>
// Global variables and functions for wizard functionality
var currentStepIndex = 0;
var totalSteps = 0;
var formQuestions = [];
var wizardConfig = {
    totalResponses: 10,
    questions: {}
};

// Initialize form questions from server data
// Using a separate script tag to avoid linter issues with Jinja2 syntax
</script>
<script>
// This script tag contains Jinja2 template syntax
formQuestions = {{ form_data.questions | tojson }};
</script>
<script>
// Global navigation functions (accessible from HTML onclick handlers)
function toggleNavMenu() {
    var header = document.querySelector('.nav-menu-header');
    var content = document.getElementById('navMenuContent');
    var chevron = document.getElementById('navChevron');

    if (header && content) {
        header.classList.toggle('open');
        content.classList.toggle('open');
    }
}

function navigateToStep(targetStep) {
    // Close navigation menu
    var header = document.querySelector('.nav-menu-header');
    var content = document.getElementById('navMenuContent');
    if (header && content) {
        header.classList.remove('open');
        content.classList.remove('open');
    }

    // Convert final step to actual index
    var targetIndex;
    if (targetStep === 'final') {
        targetIndex = totalSteps - 1;
    } else {
        targetIndex = parseInt(targetStep);
    }

    // Validate target step
    if (targetIndex < 0 || targetIndex >= totalSteps) {
        return;
    }

    // Check if we can navigate to this step
    if (targetIndex > currentStepIndex + 1) {
        // Allow jumping ahead with warning
        if (!confirm('Are you sure you want to skip ahead? Some configuration may be incomplete.')) {
            return;
        }
    }

    // Save current step data if moving forward
    if (targetIndex > currentStepIndex) {
        saveCurrentStepData();
    }

    // Update current step
    currentStepIndex = targetIndex;
    updateWizardUI();
    updateNavigationMenu();

    // If this is the final step, generate summary
    if (currentStepIndex === totalSteps - 1) {
        generateSummary();
    }
}

function updateNavigationMenu() {
    // Update navigation menu items
    $('.nav-menu-item').each(function() {
        var stepIndex = $(this).data('step');
        var isActive = (stepIndex === currentStepIndex) ||
                        (stepIndex === 'final' && currentStepIndex === totalSteps - 1);
        var isCompleted = (typeof stepIndex === 'number' && stepIndex < currentStepIndex) ||
                         (stepIndex === 'final' && currentStepIndex === totalSteps - 1);

        $(this).removeClass('active completed');

        if (isActive) {
            $(this).addClass('active');
            $(this).find('.nav-item-status i').removeClass('fas fa-circle fas fa-check').addClass('fas fa-arrow-right');
        } else if (isCompleted) {
            $(this).addClass('completed');
            $(this).find('.nav-item-status i').removeClass('fas fa-circle fas fa-arrow-right').addClass('fas fa-check');
        } else {
            $(this).find('.nav-item-status i').removeClass('fas fa-check fas fa-arrow-right').addClass('fas fa-circle');
        }
    });
}

function createNavigationItems() {
    // Find the insertion point (before the Summary item)
    var summaryItem = $('.nav-menu-item[data-step="final"]');

    // Create navigation items for each question
    formQuestions.forEach(function(question, index) {
        var stepIndex = index + 2; // After welcome and total responses steps
        var questionIcon = getQuestionIcon(question.type);

        var navItem = $('<div class="nav-menu-item" data-step="' + stepIndex + '" onclick="navigateToStep(' + stepIndex + ')">' +
            '<div class="nav-item-icon">' + questionIcon + '</div>' +
            '<div class="nav-item-text">' +
                '<div class="nav-item-title">Question ' + (index + 1) + '</div>' +
                '<div class="nav-item-subtitle">' + truncateText(question.title, 25) + '</div>' +
            '</div>' +
            '<div class="nav-item-status">' +
                '<i class="fas fa-circle"></i>' +
            '</div>' +
        '</div>');

        summaryItem.before(navItem);
    });
}

function getQuestionIcon(questionType) {
    var icons = {
        'multiple_choice': '🔘',
        'dropdown': '📋',
        'checkboxes': '☑️',
        'linear_scale': '📏',
        'rating': '⭐',
        'short_answer': '✏️',
        'paragraph': '📝'
    };
    return icons[questionType] || '❓';
}

function truncateText(text, maxLength) {
    if (text.length <= maxLength) {
        return text;
    }
    return text.substring(0, maxLength) + '...';
}

function initializeWizard() {
    // Load existing configuration if available
    loadExistingConfiguration();

    // Create question steps
    createQuestionSteps();

    // Create navigation menu items
    createNavigationItems();

    // Calculate total steps
    totalSteps = 2 + formQuestions.length + 1; // Welcome + Responses + Questions + Summary
    $('#totalSteps').text(totalSteps);

    // Create step indicators
    createStepIndicators();

    // Update UI
    updateWizardUI();

    // Bind events
    bindEvents();

    // Populate existing data into form fields
    populateExistingData();
}

function loadExistingConfiguration() {
    // Check if existing customer configuration exists
    {% if form_data.customer_config %}
    var existingConfig = {{ form_data.customer_config | tojson }};

    // Load total responses
    if (existingConfig.total_responses) {
        wizardConfig.totalResponses = existingConfig.total_responses;
    }

    // Load question configurations
    if (existingConfig.questions) {
        for (var questionId in existingConfig.questions) {
            if (existingConfig.questions.hasOwnProperty(questionId)) {
                var questionConfig = existingConfig.questions[questionId];

                // Initialize question config if not exists
                if (!wizardConfig.questions[questionId]) {
                    wizardConfig.questions[questionId] = {
                        type: questionConfig.type
                    };
                }

                // Load option-based configuration
                if (questionConfig.options) {
                    wizardConfig.questions[questionId].options = questionConfig.options;
                }

                // Load text-based configuration
                if (questionConfig.description) {
                    wizardConfig.questions[questionId].description = questionConfig.description;
                }

                if (questionConfig.examples) {
                    wizardConfig.questions[questionId].examples = questionConfig.examples;
                }
            }
        }
    }
    {% endif %}
}

function populateExistingData() {
    // Populate total responses field
    if (wizardConfig.totalResponses) {
        $('#totalResponses').val(wizardConfig.totalResponses);
    }

    // Populate question data
    formQuestions.forEach(function(question, questionIndex) {
        var questionId = question.id.toString();
        var config = wizardConfig.questions[questionId];

        if (config) {
            if (config.options) {
                // Populate option sliders
                question.options.forEach(function(option, optionIndex) {
                    var percentage = config.options[option] || Math.floor(100 / question.options.length);
                    var sliderId = '#slider_' + questionIndex + '_' + optionIndex;
                    var valueId = '#value_' + questionIndex + '_' + optionIndex;

                    $(sliderId).val(percentage);
                    $(valueId).text(percentage + '%');
                });

                // Update total display
                updateTotal(questionIndex);
            } else {
                // Populate text fields
                if (config.description) {
                    $('#description_' + questionIndex).val(config.description);
                }

                if (config.examples) {
                    config.examples.forEach(function(example, index) {
                        if (example && index < 3) {
                            $('#example_' + questionIndex + '_' + (index + 1)).val(example);
                        }
                    });
                }
            }
        }
    });
}

function createQuestionSteps() {
    var wizardContent = $('.wizard-content');

    formQuestions.forEach(function(question, index) {
        var stepIndex = index + 2; // After welcome and total responses steps
        var isOptionBased = ['multiple_choice', 'dropdown', 'checkboxes', 'linear_scale', 'rating'].includes(question.type);

        var stepHTML = '<div class="step" data-step="' + stepIndex + '">' +
            '<h2 class="step-title">Question ' + (index + 1) + '</h2>' +
            '<p class="step-description">' + question.title + '</p>';

        if (question.description) {
            stepHTML += '<div class="alert alert-info">' + question.description + '</div>';
        }

        if (isOptionBased) {
            stepHTML += createOptionBasedStep(question, index);
        } else {
            stepHTML += createTextBasedStep(question, index);
        }

        stepHTML += '</div>';

        // Insert before the final step
        wizardContent.find('.step[data-step="final"]').before(stepHTML);
    });
}

function createOptionBasedStep(question, questionIndex) {
    var html = '<div class="form-group">' +
        '<label class="form-label">Set response distribution for each option:</label>' +
        '<p class="text-muted">Adjust the percentage of responses for each option. Total must equal 100%.</p>';

    // Check if existing configuration exists for this question
    var existingConfig = wizardConfig.questions[question.id];

    question.options.forEach(function(option, optionIndex) {
        var defaultValue = Math.floor(100 / question.options.length);
        var currentValue = defaultValue;

        // Use existing value if available
        if (existingConfig && existingConfig.options && existingConfig.options[option] !== undefined) {
            currentValue = existingConfig.options[option];
        }

        html += '<div class="slider-container">' +
            '<div class="slider-label">' +
                '<span class="slider-option">' + option + '</span>' +
                '<span class="slider-value" id="value_' + questionIndex + '_' + optionIndex + '">' + currentValue + '%</span>' +
            '</div>' +
            '<input type="range" class="range-slider" ' +
                   'id="slider_' + questionIndex + '_' + optionIndex + '"' +
                   'min="0" max="100" value="' + currentValue + '"' +
                   'data-question="' + questionIndex + '" data-option="' + optionIndex + '">' +
        '</div>';
    });

    html += '<div class="total-percentage" id="total_' + questionIndex + '">Total: 100%</div></div>';

    // Initialize wizard config for this question if not already loaded
    if (!wizardConfig.questions[question.id]) {
        wizardConfig.questions[question.id] = {
            type: question.type,
            options: {}
        };

        question.options.forEach(function(option) {
            wizardConfig.questions[question.id].options[option] = Math.floor(100 / question.options.length);
        });
    }

    return html;
}

function createTextBasedStep(question, questionIndex) {
    // Check if existing configuration exists for this question
    var existingConfig = wizardConfig.questions[question.id];
    var existingDescription = (existingConfig && existingConfig.description) ? existingConfig.description : '';
    var existingExamples = (existingConfig && existingConfig.examples) ? existingConfig.examples : [];

    var html = '<div class="form-group">' +
        '<label class="form-label">Provide guidance for generating responses:</label>' +
        '<div class="example-input">' +
            '<label class="form-label">Description/Requirements</label>' +
            '<textarea class="form-control" id="description_' + questionIndex + '" ' +
                      'placeholder="Describe what kind of responses you want for this question" rows="3">' +
                      existingDescription + '</textarea>' +
        '</div>' +
        '<label class="form-label">Example Responses (Optional)</label>' +
        '<p class="text-muted">Provide up to 3 example responses to guide the AI:</p>';

    for (var i = 1; i <= 3; i++) {
        var exampleValue = (existingExamples[i-1]) ? existingExamples[i-1] : '';
        html += '<div class="example-input">' +
            '<textarea class="form-control" id="example_' + questionIndex + '_' + i + '" ' +
                      'placeholder="Example response ' + i + '" rows="2">' + exampleValue + '</textarea>' +
        '</div>';
    }

    html += '</div>';

    // Initialize wizard config for this question if not already loaded
    if (!wizardConfig.questions[question.id]) {
        wizardConfig.questions[question.id] = {
            type: question.type,
            description: existingDescription,
            examples: existingExamples.slice() // Create a copy of the array
        };
    }

    return html;
}

function createStepIndicators() {
    var indicator = $('#stepIndicator');
    indicator.empty();

    for (var i = 0; i < totalSteps; i++) {
        var dot = $('<div class="step-dot' + (i === 0 ? ' active' : '') + '"></div>');
        indicator.append(dot);
    }
}

function bindEvents() {
    // Navigation buttons
    $('#nextBtn').on('click', nextStep);
    $('#prevBtn').on('click', prevStep);

    // Total responses input
    $('#totalResponses').on('input', function() {
        wizardConfig.totalResponses = parseInt($(this).val()) || 10;
    });

    // Slider events (will be bound dynamically)
    $(document).on('input', '.range-slider', function() {
        var questionIndex = $(this).data('question');
        var optionIndex = $(this).data('option');
        var value = parseInt($(this).val());

        // Update display
        $('#value_' + questionIndex + '_' + optionIndex).text(value + '%');

        // Update config
        var question = formQuestions[questionIndex];
        var option = question.options[optionIndex];
        wizardConfig.questions[question.id].options[option] = value;

        // Calculate total
        updateTotal(questionIndex);
    });

    // Text input events
    $(document).on('input', 'textarea[id^="description_"]', function() {
        var questionIndex = parseInt($(this).attr('id').split('_')[1]);
        var question = formQuestions[questionIndex];
        wizardConfig.questions[question.id].description = $(this).val();
    });

    $(document).on('input', 'textarea[id^="example_"]', function() {
        var parts = $(this).attr('id').split('_');
        var questionIndex = parseInt(parts[1]);
        var exampleIndex = parseInt(parts[2]) - 1;
        var question = formQuestions[questionIndex];

        if (!wizardConfig.questions[question.id].examples) {
            wizardConfig.questions[question.id].examples = [];
        }

        wizardConfig.questions[question.id].examples[exampleIndex] = $(this).val();
    });
}

function updateTotal(questionIndex) {
    var question = formQuestions[questionIndex];
    var total = 0;

    for (var option in wizardConfig.questions[question.id].options) {
        if (wizardConfig.questions[question.id].options.hasOwnProperty(option)) {
            total += wizardConfig.questions[question.id].options[option];
        }
    }

    var totalElement = $('#total_' + questionIndex);
    totalElement.text('Total: ' + total + '%');

    if (total === 100) {
        totalElement.removeClass('invalid').addClass('valid');
    } else {
        totalElement.removeClass('valid').addClass('invalid');
    }

    return total === 100;
}

function nextStep() {
    // Validate current step
    if (!validateCurrentStep()) {
        return;
    }

    // Save current step data
    saveCurrentStepData();

    if (currentStepIndex < totalSteps - 1) {
        currentStepIndex++;
        updateWizardUI();

        // If this is the final step, generate summary
        if (currentStepIndex === totalSteps - 1) {
            generateSummary();
        }
    } else {
        // Submit form
        submitConfiguration();
    }
}

function prevStep() {
    if (currentStepIndex > 0) {
        currentStepIndex--;
        updateWizardUI();
    }
}

function validateCurrentStep() {
    var currentStep = $('.step[data-step="' + currentStepIndex + '"]');

    if (currentStepIndex === 1) {
        // Validate total responses
        var totalResponses = parseInt($('#totalResponses').val());
        if (!totalResponses || totalResponses < 1) {
            alert('Please enter a valid number of responses (minimum 1).');
            return false;
        }
    }

    // Validate option-based questions
    if (currentStepIndex >= 2 && currentStepIndex < totalSteps - 1) {
        var questionIndex = currentStepIndex - 2;
        var question = formQuestions[questionIndex];

        if (['multiple_choice', 'dropdown', 'checkboxes', 'linear_scale', 'rating'].includes(question.type)) {
            if (!updateTotal(questionIndex)) {
                alert('Please ensure the total percentage equals 100% for all options.');
                return false;
            }
        }
    }

    return true;
}

function saveCurrentStepData() {
    if (currentStepIndex === 1) {
        wizardConfig.totalResponses = parseInt($('#totalResponses').val()) || 10;
    }
}

function updateWizardUI() {
    // Update progress bar
    var progress = ((currentStepIndex + 1) / totalSteps) * 100;
    $('#progressBar').css('width', progress + '%');

    // Update step counter
    $('#currentStep').text(currentStepIndex + 1);

    // Show/hide steps
    $('.step').removeClass('active');
    $('.step[data-step="' + currentStepIndex + '"]').addClass('active');

    // Update step indicators
    $('.step-dot').removeClass('active completed');
    $('.step-dot').each(function(index) {
        if (index < currentStepIndex) {
            $(this).addClass('completed');
        } else if (index === currentStepIndex) {
            $(this).addClass('active');
        }
    });

    // Update navigation buttons
    if (currentStepIndex === 0) {
        $('#prevBtn').hide();
    } else {
        $('#prevBtn').show();
    }

    if (currentStepIndex === totalSteps - 1) {
        $('#nextBtn').html('<i class="fas fa-save"></i> Save Configuration');
    } else {
        $('#nextBtn').html('Next <i class="fas fa-arrow-right"></i>');
    }

    // Update navigation menu
    updateNavigationMenu();
}

function generateSummary() {
    var summaryHTML = '<div class="summary-card">' +
        '<div class="summary-title">📊 Total Responses</div>' +
        '<div class="summary-content">' + wizardConfig.totalResponses + ' responses will be generated</div>' +
    '</div>';

    formQuestions.forEach(function(question, index) {
        var config = wizardConfig.questions[question.id];
        summaryHTML += '<div class="summary-card">' +
            '<div class="summary-title">❓ ' + question.title + '</div>' +
            '<div class="summary-content">';

        if (config.options) {
            summaryHTML += 'Response Distribution:<br>';
            for (var option in config.options) {
                if (config.options.hasOwnProperty(option)) {
                    var percentage = config.options[option];
                    summaryHTML += '• ' + option + ': ' + percentage + '%<br>';
                }
            }
        } else {
            if (config.description) {
                summaryHTML += 'Description: ' + config.description + '<br>';
            }
            var examples = config.examples.filter(function(ex) { return ex && ex.trim(); });
            if (examples.length > 0) {
                summaryHTML += 'Examples: ' + examples.join(', ');
            }
        }

        summaryHTML += '</div></div>';
    });

    $('#configurationSummary').html(summaryHTML);
}

function submitConfiguration() {
    // Prepare form data
    $('#hiddenTotalResponses').val(wizardConfig.totalResponses);

    var hiddenInputs = '';

    formQuestions.forEach(function(question) {
        var config = wizardConfig.questions[question.id];

        if (config.options) {
            // Option-based question
            for (var option in config.options) {
                if (config.options.hasOwnProperty(option)) {
                    var fieldName = 'question_' + question.id + '_option_' + option.replace(/\s+/g, '_');
                    hiddenInputs += '<input type="hidden" name="' + fieldName + '" value="' + config.options[option] + '">';
                }
            }
        } else {
            // Text-based question
            if (config.description) {
                hiddenInputs += '<input type="hidden" name="question_' + question.id + '_description" value="' + config.description + '">';
            }

            config.examples.forEach(function(example, index) {
                if (example && example.trim()) {
                    hiddenInputs += '<input type="hidden" name="question_' + question.id + '_example_' + (index + 1) + '" value="' + example + '">';
                }
            });
        }
    });

    $('#hiddenQuestionInputs').html(hiddenInputs);

    // Submit the form
    $('#wizardForm').submit();
}

// Initialize wizard when document is ready
$(document).ready(function() {
    initializeWizard();
});
</script>
{% endblock %}