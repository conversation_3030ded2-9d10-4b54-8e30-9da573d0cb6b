{% extends "base.html" %}

{% block title %}Form Details - Google Form AutoFill{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i>{{ form_data.form_title }}
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <p>
                            <strong>Form ID:</strong> {{ form_id }}<br>
                            {% if form_data.form_description %}
                            <strong>Description:</strong> {{ form_data.form_description }}<br>
                            {% endif %}
                            <strong>Questions:</strong> {{ form_data.questions|length }}
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="btn-group">
                            <a href="{{ url_for('generate_responses', form_id=form_id) }}" class="btn btn-success">
                                <i class="fas fa-magic me-1"></i>Generate Responses
                            </a>
                            <a href="{{ url_for('review_responses', form_id=form_id) }}" class="btn btn-warning">
                                <i class="fas fa-edit me-1"></i>Review Responses
                            </a>
                            <a href="{{ url_for('submit_form', form_id=form_id) }}" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-1"></i>Submit Form
                            </a>
                            <a href="{{ url_for('customer_form', form_id=form_id) }}" class="btn btn-info">
                                <i class="fas fa-user-cog me-1"></i>Customer Interface
                            </a>
                            <a href="{{ url_for('customer_form_wizard', form_id=form_id) }}" class="btn btn-outline-info">
                                <i class="fas fa-magic me-1"></i>Wizard Interface
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>Form Questions</h5>
            </div>
            <div class="card-body">
                <div class="accordion" id="questionsAccordion">
                    {% for question in form_data.questions %}
                    {% set outer_loop_index = loop.index %}
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="heading{{ outer_loop_index }}">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ outer_loop_index }}">
                                <span class="badge {% if question.required %}bg-danger{% else %}bg-secondary{% endif %} me-2">
                                    {% if question.required %}Required{% else %}Optional{% endif %}
                                </span>
                                {{ question.title }}
                                <span class="badge bg-info ms-2">{{ question.type_name }}</span>
                            </button>
                        </h2>
                        <div id="collapse{{ outer_loop_index }}" class="accordion-collapse collapse" data-bs-parent="#questionsAccordion">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Question ID:</strong> {{ question.id }}</p>
                                        <p><strong>Type:</strong> {{ question.type_name }}</p>
                                        {% if question.description %}
                                        <p><strong>Description:</strong> {{ question.description }}</p>
                                        {% endif %}
                                        <p><strong>Required:</strong> {% if question.required %}Yes{% else %}No{% endif %}</p>
                                        <p><strong>Open-ended:</strong> {% if question.is_open_ended %}Yes{% else %}No{% endif %}</p>
                                    </div>
                                    <div class="col-md-6">
                                        {% if question.options %}
                                        <p><strong>Options:</strong></p>
                                        <ul class="list-group">
                                            {% for option in question.options %}
                                            <li class="list-group-item">{{ option }}</li>
                                            {% endfor %}
                                        </ul>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="mt-3">
                                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#generateModal{{ outer_loop_index }}">
                                        <i class="fas fa-pencil-alt me-1"></i>Generate for This Question
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Single Question Generation Modal -->
                    <div class="modal fade" id="generateModal{{ outer_loop_index }}" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Generate Responses for Question</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <h6>{{ question.title }}</h6>
                                    <p>
                                        <span class="badge bg-info">{{ question.type_name }}</span>
                                        <span class="badge {% if question.required %}bg-danger{% else %}bg-secondary{% endif %}">
                                            {% if question.required %}Required{% else %}Optional{% endif %}
                                        </span>
                                    </p>

                                    <ul class="nav nav-tabs" id="questionGenTabs{{ outer_loop_index }}" role="tablist">
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link active" id="manual-tab{{ outer_loop_index }}" data-bs-toggle="tab" data-bs-target="#manual{{ outer_loop_index }}" type="button" role="tab" aria-controls="manual{{ outer_loop_index }}" aria-selected="true">
                                                Manual Input
                                            </button>
                                        </li>
                                        {% if question.is_open_ended %}
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="ai-tab{{ outer_loop_index }}" data-bs-toggle="tab" data-bs-target="#ai{{ outer_loop_index }}" type="button" role="tab" aria-controls="ai{{ outer_loop_index }}" aria-selected="false">
                                                AI-Assisted
                                            </button>
                                        </li>
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link" id="auto-tab{{ outer_loop_index }}" data-bs-toggle="tab" data-bs-target="#auto{{ outer_loop_index }}" type="button" role="tab" aria-controls="auto{{ outer_loop_index }}" aria-selected="false">
                                                Fully Automated
                                            </button>
                                        </li>
                                        {% endif %}
                                    </ul>

                                    <div class="tab-content p-3 border border-top-0 rounded-bottom">
                                        <!-- Manual Input -->
                                        <div class="tab-pane fade show active" id="manual{{ outer_loop_index }}" role="tabpanel" aria-labelledby="manual-tab{{ outer_loop_index }}">
                                            <form id="manualForm{{ outer_loop_index }}" class="needs-validation" novalidate>
                                                {% if question.options %}
                                                    {% if question.type == "linear_scale" %}
                                                    <p>Configure Linear Scale question (numeric scale):</p>
                                                    <div class="mb-3">
                                                        <div class="alert alert-info">
                                                            <i class="fas fa-ruler-horizontal me-2"></i>
                                                            <strong>Linear Scale:</strong> This is a numeric scale from {{ question.options[0] }} to {{ question.options[-1] }}.
                                                            Assign weights to influence the distribution of responses across the scale.
                                                        </div>
                                                    </div>
                                                    {% for option in question.options %}
                                                    <div class="mb-3 row">
                                                        <label class="col-sm-8 col-form-label">
                                                            <i class="fas fa-circle text-primary me-1"></i>
                                                            Scale {{ option }}
                                                        </label>
                                                        <div class="col-sm-4">
                                                            <input type="number" class="form-control" name="weight_{{ loop.index0 }}" value="1" min="0">
                                                        </div>
                                                    </div>
                                                    {% endfor %}
                                                    <input type="hidden" name="question_id" value="{{ question.id }}">
                                                    <input type="hidden" name="method" value="manual_linear_scale">
                                                    {% elif question.type == "checkbox_grid" %}
                                                    <p>Configure Checkbox Grid combinations (multiple selections per row):</p>
                                                    <div class="mb-3">
                                                        <div class="alert alert-info">
                                                            <i class="fas fa-table me-2"></i>
                                                            <strong>Checkbox Grid:</strong> Create multiple combinations of row-column selections with different weights to control the distribution of generated responses.
                                                        </div>
                                                    </div>

                                                    <!-- Available Grid Display -->
                                                    {% if question.grid_rows and question.grid_columns %}
                                                    <div class="mb-3">
                                                        <h6>Available Grid Options:</h6>
                                                        <div class="table-responsive">
                                                            <table class="table table-bordered table-sm">
                                                                <thead>
                                                                    <tr>
                                                                        <th>Row</th>
                                                                        {% for column in question.grid_columns %}
                                                                        <th class="text-center">{{ column }}</th>
                                                                        {% endfor %}
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    {% for row in question.grid_rows %}
                                                                    <tr>
                                                                        <td><strong>{{ row }}</strong></td>
                                                                        {% for column in question.grid_columns %}
                                                                        <td class="text-center">
                                                                            <span class="badge bg-light text-dark border">{{ row }}: {{ column }}</span>
                                                                        </td>
                                                                        {% endfor %}
                                                                    </tr>
                                                                    {% endfor %}
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>

                                                    <!-- Checkbox Grid Combinations Container -->
                                                    <div id="checkboxGridCombinationsContainer{{ outer_loop_index }}" class="checkbox-grid-combinations-container">
                                                        <!-- Checkbox grid combinations will be dynamically added here -->
                                                    </div>

                                                    <div class="mb-3">
                                                        <button type="button" class="btn btn-outline-primary btn-sm add-checkbox-grid-combination" data-question-index="{{ outer_loop_index }}">
                                                            <i class="fas fa-plus me-1"></i>Add Grid Combination
                                                        </button>
                                                    </div>
                                                    {% endif %}

                                                    <input type="hidden" name="question_id" value="{{ question.id }}">
                                                    <input type="hidden" name="method" value="manual_checkbox_grid_combinations">
                                                    {% elif question.type == "multiple_choice_grid" %}
                                                    <p>Configure Multiple Choice Grid combinations (single selection per row):</p>
                                                    <div class="mb-3">
                                                        <div class="alert alert-info">
                                                            <i class="fas fa-table me-2"></i>
                                                            <strong>Multiple Choice Grid:</strong> Create multiple combinations of row-column selections with different weights to control the distribution of generated responses.
                                                        </div>
                                                    </div>

                                                    <!-- Available Grid Display -->
                                                    {% if question.grid_rows and question.grid_columns %}
                                                    <div class="mb-3">
                                                        <h6>Available Grid Options:</h6>
                                                        <div class="table-responsive">
                                                            <table class="table table-bordered table-sm">
                                                                <thead>
                                                                    <tr>
                                                                        <th>Row</th>
                                                                        {% for column in question.grid_columns %}
                                                                        <th class="text-center">{{ column }}</th>
                                                                        {% endfor %}
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    {% for row in question.grid_rows %}
                                                                    <tr>
                                                                        <td><strong>{{ row }}</strong></td>
                                                                        {% for column in question.grid_columns %}
                                                                        <td class="text-center">
                                                                            <span class="badge bg-light text-dark border">{{ row }}: {{ column }}</span>
                                                                        </td>
                                                                        {% endfor %}
                                                                    </tr>
                                                                    {% endfor %}
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>

                                                    <!-- Multiple Choice Grid Combinations Container -->
                                                    <div id="multiGridCombinationsContainer{{ outer_loop_index }}" class="multi-grid-combinations-container">
                                                        <!-- Multiple choice grid combinations will be dynamically added here -->
                                                    </div>

                                                    <div class="mb-3">
                                                        <button type="button" class="btn btn-outline-primary btn-sm add-multi-grid-combination" data-question-index="{{ outer_loop_index }}">
                                                            <i class="fas fa-plus me-1"></i>Add Grid Combination
                                                        </button>
                                                    </div>
                                                    {% endif %}

                                                    <input type="hidden" name="question_id" value="{{ question.id }}">
                                                    <input type="hidden" name="method" value="manual_multiple_choice_grid_combinations">
                                                    {% elif question.type == "checkboxes" %}
                                                    <p>Configure checkbox combinations (multiple selections allowed):</p>
                                                    <div class="mb-3">
                                                        <div class="alert alert-info">
                                                            <i class="fas fa-check-square me-2"></i>
                                                            <strong>Checkbox Question:</strong> Create multiple combinations of options with different weights to control the distribution of generated responses.
                                                        </div>
                                                    </div>

                                                    <!-- Available Options Display -->
                                                    <div class="mb-3">
                                                        <h6>Available Options:</h6>
                                                        <div class="row">
                                                            {% for option in question.options %}
                                                            <div class="col-md-6 mb-2">
                                                                <span class="badge bg-light text-dark border">{{ option }}</span>
                                                            </div>
                                                            {% endfor %}
                                                        </div>
                                                    </div>

                                                    <!-- Checkbox Combinations Container -->
                                                    <div id="checkboxCombinationsContainer{{ outer_loop_index }}" class="checkbox-combinations-container">
                                                        <!-- Checkbox combinations will be dynamically added here -->
                                                    </div>

                                                    <div class="mb-3">
                                                        <button type="button" class="btn btn-outline-primary btn-sm add-checkbox-combination" data-question-index="{{ outer_loop_index }}">
                                                            <i class="fas fa-plus me-1"></i>Add Checkbox Combination
                                                        </button>
                                                    </div>

                                                    <input type="hidden" name="question_id" value="{{ question.id }}">
                                                    <input type="hidden" name="method" value="manual_checkbox_combinations">
                                                    {% elif question.type == "rating" %}
                                                    <p>Assign weights to the rating values (higher weight = more likely to be selected):</p>
                                                    <div class="mb-3">
                                                        <div class="alert alert-info">
                                                            <i class="fas fa-info-circle me-2"></i>
                                                            <strong>Rating Question:</strong> This is a rating scale from {{ question.options[0] }} to {{ question.options[-1] }}.
                                                            Assign weights to influence the distribution of responses across the rating scale.
                                                        </div>
                                                    </div>
                                                    {% for option in question.options %}
                                                    <div class="mb-3 row">
                                                        <label class="col-sm-8 col-form-label">
                                                            <i class="fas fa-star text-warning me-1"></i>
                                                            Rating {{ option }}
                                                        </label>
                                                        <div class="col-sm-4">
                                                            <input type="number" class="form-control" name="weight_{{ loop.index0 }}" value="1" min="0">
                                                        </div>
                                                    </div>
                                                    {% endfor %}
                                                    <input type="hidden" name="question_id" value="{{ question.id }}">
                                                    <input type="hidden" name="method" value="manual_rating">
                                                    {% else %}
                                                    <p>Assign weights to the available options (responses will be generated based on these weights):</p>
                                                    {% for option in question.options %}
                                                    <div class="mb-3 row">
                                                        <label class="col-sm-8 col-form-label">{{ option }}</label>
                                                        <div class="col-sm-4">
                                                            <input type="number" class="form-control" name="weight_{{ loop.index0 }}" value="1" min="0">
                                                        </div>
                                                    </div>
                                                    {% endfor %}
                                                    <input type="hidden" name="question_id" value="{{ question.id }}">
                                                    <input type="hidden" name="method" value="manual_options">
                                                    {% endif %}
                                                {% elif question.type == "date" %}
                                                    <p>Configure date ranges for random date generation (multiple ranges allowed):</p>
                                                    <div class="mb-3">
                                                        <div class="alert alert-info">
                                                            <i class="fas fa-calendar-alt me-2"></i>
                                                            <strong>Date Question:</strong> Add multiple date ranges with different weights to control the distribution of generated dates.
                                                        </div>
                                                    </div>

                                                    <div id="dateRangesContainer{{ outer_loop_index }}" class="date-ranges-container">
                                                        <!-- Date ranges will be dynamically added here -->
                                                    </div>

                                                    <div class="mb-3">
                                                        <button type="button" class="btn btn-outline-primary btn-sm add-date-range" data-question-index="{{ outer_loop_index }}">
                                                            <i class="fas fa-plus me-1"></i>Add Date Range
                                                        </button>
                                                    </div>

                                                    <input type="hidden" name="question_id" value="{{ question.id }}">
                                                    <input type="hidden" name="method" value="manual_date">
                                                {% elif question.type == "time" %}
                                                    <p>Configure time ranges for random time generation (multiple ranges allowed):</p>
                                                    <div class="mb-3">
                                                        <div class="alert alert-info">
                                                            <i class="fas fa-clock me-2"></i>
                                                            <strong>Time Question:</strong> Add multiple time ranges with different weights to control the distribution of generated times.
                                                        </div>
                                                    </div>

                                                    <div id="timeRangesContainer{{ outer_loop_index }}" class="time-ranges-container">
                                                        <!-- Time ranges will be dynamically added here -->
                                                    </div>

                                                    <div class="mb-3">
                                                        <button type="button" class="btn btn-outline-primary btn-sm add-time-range" data-question-index="{{ outer_loop_index }}">
                                                            <i class="fas fa-plus me-1"></i>Add Time Range
                                                        </button>
                                                    </div>

                                                    <input type="hidden" name="question_id" value="{{ question.id }}">
                                                    <input type="hidden" name="method" value="manual_time">
                                                {% else %}
                                                    <div class="mb-3">
                                                        <label class="form-label">Enter responses (one per line):</label>
                                                        <textarea class="form-control" name="responses" rows="5"></textarea>
                                                        <div class="form-text">Each line will be treated as a separate response with weight 1.</div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="add_weights" id="addWeights{{ outer_loop_index }}">
                                                            <label class="form-check-label" for="addWeights{{ outer_loop_index }}">
                                                                Add weights to responses
                                                            </label>
                                                        </div>
                                                        <div id="weightFormat{{ outer_loop_index }}" class="form-text d-none">
                                                            Format: "Response text [weight]" (e.g., "John Smith [3]")
                                                        </div>
                                                    </div>
                                                    <input type="hidden" name="question_id" value="{{ question.id }}">
                                                    <input type="hidden" name="method" value="manual_text">
                                                {% endif %}
                                            </form>
                                        </div>

                                        {% if question.is_open_ended %}
                                        <!-- AI-Assisted -->
                                        <div class="tab-pane fade" id="ai{{ outer_loop_index }}" role="tabpanel" aria-labelledby="ai-tab{{ outer_loop_index }}">
                                            <form id="aiForm{{ outer_loop_index }}" class="needs-validation" novalidate>
                                                <div class="mb-3">
                                                    <label class="form-label">AI Provider:</label>
                                                    <select class="form-select ai-provider-select" name="provider" data-question-index="{{ outer_loop_index }}">
                                                        <option value="gemini">Google Gemini</option>
                                                        <option value="openrouter">OpenRouter</option>
                                                    </select>
                                                    <div class="form-text">
                                                        Select the AI provider to use for generating responses.
                                                    </div>
                                                </div>

                                                <div class="mb-3">
                                                    <label class="form-label">AI Model:</label>
                                                    <select class="form-select ai-model-select" name="model" data-question-index="{{ outer_loop_index }}">
                                                        <option value="">Loading models...</option>
                                                    </select>
                                                    <div class="form-text">
                                                        Select the AI model to use. Models will be loaded based on the selected provider.
                                                    </div>
                                                </div>

                                                <div class="mb-3">
                                                    <label class="form-label">Prompt for AI:</label>
                                                    <textarea class="form-control" name="prompt" rows="3"
                                                        placeholder="E.g., 'Generate Malaysian names' or 'Create responses about favorite foods'"></textarea>
                                                    <div class="form-text">
                                                        Customize the prompt to get more relevant responses from the AI.
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">Number of responses:</label>
                                                    <input type="number" class="form-control" name="count" value="5" min="1" max="20">
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">API Key (Optional):</label>
                                                    <input type="text" class="form-control" name="api_key" value="">
                                                    <div class="form-text">
                                                        Optional: Provide a specific API key for this request. If left empty, the configured keys will be used.
                                                    </div>
                                                </div>
                                                <input type="hidden" name="question_id" value="{{ question.id }}">
                                                <input type="hidden" name="method" value="ai_assisted">
                                            </form>
                                        </div>

                                        <!-- Fully Automated -->
                                        <div class="tab-pane fade" id="auto{{ outer_loop_index }}" role="tabpanel" aria-labelledby="auto-tab{{ outer_loop_index }}">
                                            <form id="autoForm{{ outer_loop_index }}" class="needs-validation" novalidate>
                                                <div class="mb-3">
                                                    <label class="form-label">AI Provider:</label>
                                                    <select class="form-select ai-provider-select" name="provider" data-question-index="{{ outer_loop_index }}">
                                                        <option value="gemini">Google Gemini</option>
                                                        <option value="openrouter">OpenRouter</option>
                                                    </select>
                                                    <div class="form-text">
                                                        Select the AI provider to use for generating responses.
                                                    </div>
                                                </div>

                                                <div class="mb-3">
                                                    <label class="form-label">AI Model:</label>
                                                    <select class="form-select ai-model-select" name="model" data-question-index="{{ outer_loop_index }}">
                                                        <option value="">Loading models...</option>
                                                    </select>
                                                    <div class="form-text">
                                                        Select the AI model to use. Models will be loaded based on the selected provider.
                                                    </div>
                                                </div>

                                                <div class="mb-3">
                                                    <label class="form-label" for="autoPrompt{{ outer_loop_index }}">Requirements/Instructions:</label>
                                                    <textarea class="form-control" id="autoPrompt{{ outer_loop_index }}" name="prompt" rows="3"
                                                        placeholder="E.g., 'Give me the list of names General in Malaysia' or 'Generate responses about favorite Malaysian foods'"></textarea>
                                                    <div class="form-text">
                                                        Specify your requirements to guide the AI in generating relevant responses. This helps avoid random generation.
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">Number of responses:</label>
                                                    <input type="number" class="form-control" name="count" value="10" min="1" max="50">
                                                </div>
                                                <div class="mb-3">
                                                    <label class="form-label">API Key (Optional):</label>
                                                    <input type="text" class="form-control" name="api_key" value="">
                                                    <div class="form-text">
                                                        Optional: Provide a specific API key for this request. If left empty, the configured keys will be used.
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="checkbox" name="prevent_duplicates" id="preventDuplicates{{ outer_loop_index }}" checked>
                                                        <label class="form-check-label" for="preventDuplicates{{ outer_loop_index }}">
                                                            Prevent duplicate responses
                                                        </label>
                                                    </div>
                                                </div>
                                                <input type="hidden" name="question_id" value="{{ question.id }}">
                                                <input type="hidden" name="method" value="fully_automated">
                                            </form>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-primary generate-for-question" data-question-index="{{ outer_loop_index }}">
                                        Generate Responses
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Store question options for JavaScript access
    window.questionOptions = {
        {% for question in form_data.questions %}
        {% set outer_loop_index = loop.index %}
        {{ outer_loop_index }}: [
            {% if question.options %}
                {% for option in question.options %}
                "{{ option|replace('"', '\\"') }}"{% if not loop.last %},{% endif %}
                {% endfor %}
            {% endif %}
        ]{% if not loop.last %},{% endif %}
        {% endfor %}
    };

    // Store grid data for JavaScript access
    window.questionGridData = {
        {% for question in form_data.questions %}
        {% set outer_loop_index = loop.index %}
        {{ outer_loop_index }}: {
            rows: [
                {% if question.grid_rows %}
                    {% for row in question.grid_rows %}
                    "{{ row|replace('"', '\\"') }}"{% if not loop.last %},{% endif %}
                    {% endfor %}
                {% endif %}
            ],
            columns: [
                {% if question.grid_columns %}
                    {% for column in question.grid_columns %}
                    "{{ column|replace('"', '\\"') }}"{% if not loop.last %},{% endif %}
                    {% endfor %}
                {% endif %}
            ]
        }{% if not loop.last %},{% endif %}
        {% endfor %}
    };

    $(document).ready(function() {
        // Use the global modal management utility
        AppUtils.safelyManageModals();

        // Prevent multiple event bindings and cursor-related issues
        const pageInitialized = 'formDetailsPageInitialized';
        if (window[pageInitialized]) {
            return;
        }
        window[pageInitialized] = true;

        // Disable transitions temporarily to prevent flashing on initial load
        $('body').addClass('no-transitions');
        setTimeout(() => {
            $('body').removeClass('no-transitions');
        }, 100);

        // Initialize checkbox combinations UI for all checkbox questions
        $('input[name="method"][value="manual_checkbox_combinations"]').each(function() {
            const form = $(this).closest('form');
            const formId = form.attr('id');
            const questionIndex = formId.replace('manualForm', '');

            // Add initial checkbox combination
            addCheckboxCombination(questionIndex);
        });

        // Initialize checkbox grid combinations UI
        $('input[name="method"][value="manual_checkbox_grid_combinations"]').each(function() {
            const form = $(this).closest('form');
            const formId = form.attr('id');
            const questionIndex = formId.replace('manualForm', '');

            // Add initial checkbox grid combination
            addCheckboxGridCombination(questionIndex);
        });

        // Initialize multiple choice grid combinations UI
        $('input[name="method"][value="manual_multiple_choice_grid_combinations"]').each(function() {
            const form = $(this).closest('form');
            const formId = form.attr('id');
            const questionIndex = formId.replace('manualForm', '');

            // Add initial multiple choice grid combination
            addMultiGridCombination(questionIndex);
        });

        // Initialize date and time range UI for date/time questions
        $('input[name="method"][value="manual_date"], input[name="method"][value="manual_time"]').each(function() {
            const form = $(this).closest('form');
            const formId = form.attr('id');
            const questionIndex = formId.replace('manualForm', '');
            const method = $(this).val();

            if (method === 'manual_date') {
                // Add initial date range
                addDateRange(questionIndex);
            } else if (method === 'manual_time') {
                // Add initial time range
                addTimeRange(questionIndex);
            }
        });

        // Initialize Bootstrap tabs - use event delegation to prevent multiple bindings
        $(document).off('click', '.nav-tabs button').on('click', '.nav-tabs button', function (e) {
            e.preventDefault();
            $(this).tab('show');
        });

        // Fix for modal tabs - ensure they work correctly when modal is shown
        $(document).off('shown.bs.modal', '.modal').on('shown.bs.modal', '.modal', function () {
            // Ensure the active tab is properly highlighted
            const modalId = $(this).attr('id');
            const questionIndex = modalId.replace('generateModal', '');

            // Trigger a click on the active tab to ensure proper initialization
            const activeTab = $('#questionGenTabs' + questionIndex + ' .nav-link.active');
            if (activeTab.length) {
                activeTab.tab('show');
            }

            // Initialize UI based on question type
            const form = document.getElementById('manualForm' + questionIndex);
            if (form) {
                const method = form.querySelector('input[name="method"]')?.value;

                if (method === 'manual_checkbox_combinations') {
                    // Initialize checkbox combinations if container is empty
                    const container = $(`#checkboxCombinationsContainer${questionIndex}`);
                    if (container.children().length === 0) {
                        addCheckboxCombination(questionIndex);
                    }
                } else if (method === 'manual_checkbox_grid_combinations') {
                    // Initialize checkbox grid combinations if container is empty
                    const container = $(`#checkboxGridCombinationsContainer${questionIndex}`);
                    if (container.children().length === 0) {
                        addCheckboxGridCombination(questionIndex);
                    }
                } else if (method === 'manual_multiple_choice_grid_combinations') {
                    // Initialize multiple choice grid combinations if container is empty
                    const container = $(`#multiGridCombinationsContainer${questionIndex}`);
                    if (container.children().length === 0) {
                        addMultiGridCombination(questionIndex);
                    }
                } else if (method === 'manual_date') {
                    // Initialize date ranges if container is empty
                    const container = $(`#dateRangesContainer${questionIndex}`);
                    if (container.children().length === 0) {
                        addDateRange(questionIndex);
                    }
                } else if (method === 'manual_time') {
                    // Initialize time ranges if container is empty
                    const container = $(`#timeRangesContainer${questionIndex}`);
                    if (container.children().length === 0) {
                        addTimeRange(questionIndex);
                    }
                }
            }
        });

        // Toggle weight format info
        $('input[name="add_weights"]').change(function() {
            const index = $(this).attr('id').replace('addWeights', '');
            if ($(this).is(':checked')) {
                $('#weightFormat' + index).removeClass('d-none');
            } else {
                $('#weightFormat' + index).addClass('d-none');
            }
        });

        // Handle checkbox options for checkbox-type questions
        $('.select-all-options').change(function() {
            const questionIndex = $(this).data('question-index');
            const isChecked = $(this).is(':checked');

            // Select or deselect all options
            $(`.checkbox-option[data-question-index="${questionIndex}"]`).prop('checked', isChecked);

            // Update individual weight inputs
            updateIndividualWeightInputs(questionIndex);
        });

        // Handle individual checkbox option changes
        $('.checkbox-option').change(function() {
            const questionIndex = $(this).data('question-index');

            // Update select all checkbox state
            updateSelectAllCheckbox(questionIndex);

            // Update individual weight inputs
            updateIndividualWeightInputs(questionIndex);
        });

        // Handle checkbox grid option changes
        $(document).on('change', '.checkbox-grid-option', function() {
            const questionIndex = $(this).data('question-index');
            updateGridWeightInputs(questionIndex);
        });

        // Handle radio grid option changes
        $(document).on('change', '.radio-grid-option', function() {
            const questionIndex = $(this).data('question-index');
            updateMultiGridWeightInputs(questionIndex);
        });

        // Toggle weight inputs visibility
        $('input[id^="useWeights"]').change(function() {
            const index = $(this).attr('id').replace('useWeights', '');
            if ($(this).is(':checked')) {
                $(`#weightInputs${index}`).removeClass('d-none');
            } else {
                $(`#weightInputs${index}`).addClass('d-none');
            }
        });

        // Toggle grid weight inputs visibility
        $('input[id^="useGridWeights"]').change(function() {
            const index = $(this).attr('id').replace('useGridWeights', '');
            if ($(this).is(':checked')) {
                $(`#gridWeightInputs${index}`).removeClass('d-none');
            } else {
                $(`#gridWeightInputs${index}`).addClass('d-none');
            }
        });

        // Toggle multi grid weight inputs visibility
        $('input[id^="useMultiGridWeights"]').change(function() {
            const index = $(this).attr('id').replace('useMultiGridWeights', '');
            if ($(this).is(':checked')) {
                $(`#multiGridWeightInputs${index}`).removeClass('d-none');
            } else {
                $(`#multiGridWeightInputs${index}`).addClass('d-none');
            }
        });

        // Handle default weight changes
        $('.default-weight').change(function() {
            const index = $(this).attr('id').replace('defaultWeight', '');
            const defaultWeight = $(this).val();

            // Update all individual weight inputs with the default value
            $(`#individualWeights${index} input[type="number"]`).val(defaultWeight);
        });

        // Handle default grid weight changes
        $('.default-grid-weight').change(function() {
            const index = $(this).attr('id').replace('defaultGridWeight', '');
            const defaultWeight = $(this).val();

            // Update all individual grid weight inputs with the default value
            $(`#individualGridWeights${index} input[type="number"]`).val(defaultWeight);
        });

        // Handle default multi grid weight changes
        $('.default-multi-grid-weight').change(function() {
            const index = $(this).attr('id').replace('defaultMultiGridWeight', '');
            const defaultWeight = $(this).val();

            // Update all individual multi grid weight inputs with the default value
            $(`#individualMultiGridWeights${index} input[type="number"]`).val(defaultWeight);
        });

        // Handle add date range button
        $(document).on('click', '.add-date-range', function() {
            const questionIndex = $(this).data('question-index');
            addDateRange(questionIndex);
        });

        // Handle add time range button
        $(document).on('click', '.add-time-range', function() {
            const questionIndex = $(this).data('question-index');
            addTimeRange(questionIndex);
        });

        // Handle add checkbox combination button
        $(document).on('click', '.add-checkbox-combination', function() {
            const questionIndex = $(this).data('question-index');
            addCheckboxCombination(questionIndex);
        });

        // Handle add checkbox grid combination button
        $(document).on('click', '.add-checkbox-grid-combination', function() {
            const questionIndex = $(this).data('question-index');
            addCheckboxGridCombination(questionIndex);
        });

        // Handle add multi grid combination button
        $(document).on('click', '.add-multi-grid-combination', function() {
            const questionIndex = $(this).data('question-index');
            addMultiGridCombination(questionIndex);
        });

        // Handle remove date/time range button
        $(document).on('click', '.remove-range', function() {
            $(this).closest('.range-item').remove();
        });

        // Handle remove checkbox combination button
        $(document).on('click', '.remove-combination', function() {
            $(this).closest('.combination-item').remove();
        });

        // Function to update the "Select All" checkbox state
        function updateSelectAllCheckbox(questionIndex) {
            const allOptions = $(`.checkbox-option[data-question-index="${questionIndex}"]`);
            const checkedOptions = allOptions.filter(':checked');
            const selectAllCheckbox = $(`#selectAllOptions${questionIndex}`);

            if (checkedOptions.length === 0) {
                selectAllCheckbox.prop('checked', false);
                selectAllCheckbox.prop('indeterminate', false);
            } else if (checkedOptions.length === allOptions.length) {
                selectAllCheckbox.prop('checked', true);
                selectAllCheckbox.prop('indeterminate', false);
            } else {
                selectAllCheckbox.prop('checked', false);
                selectAllCheckbox.prop('indeterminate', true);
            }
        }

        // Function to update individual weight inputs based on selected checkboxes
        function updateIndividualWeightInputs(questionIndex) {
            const selectedOptions = $(`.checkbox-option[data-question-index="${questionIndex}"]:checked`);
            const weightsContainer = $(`#individualWeights${questionIndex}`);
            const defaultWeight = $(`#defaultWeight${questionIndex}`).val();

            // Clear existing weight inputs
            weightsContainer.empty();

            // Add weight inputs for each selected option
            selectedOptions.each(function() {
                const optionValue = $(this).val();
                const optionIndex = $(this).attr('name').replace('option_', '');

                const weightInput = `
                    <div class="mb-2 row">
                        <label class="col-sm-8 col-form-label">${optionValue}</label>
                        <div class="col-sm-4">
                            <input type="number" class="form-control"
                                   name="weight_${optionIndex}"
                                   value="${defaultWeight}" min="1">
                        </div>
                    </div>
                `;

                weightsContainer.append(weightInput);
            });
        }

        // Function to update grid weight inputs based on selected checkbox grid options
        function updateGridWeightInputs(questionIndex) {
            const selectedOptions = $(`.checkbox-grid-option[data-question-index="${questionIndex}"]:checked`);
            const weightsContainer = $(`#individualGridWeights${questionIndex}`);
            const defaultWeight = $(`#defaultGridWeight${questionIndex}`).val();

            // Clear existing weight inputs
            weightsContainer.empty();

            // Add weight inputs for each selected combination
            selectedOptions.each(function(index) {
                const combinationValue = $(this).val();
                const row = $(this).data('row');
                const column = $(this).data('column');

                const weightInput = `
                    <div class="mb-2 row">
                        <label class="col-sm-8 col-form-label">${combinationValue}</label>
                        <div class="col-sm-4">
                            <input type="number" class="form-control"
                                   name="grid_weight_${index}"
                                   value="${defaultWeight}" min="1">
                        </div>
                    </div>
                `;

                weightsContainer.append(weightInput);
            });
        }

        // Function to update multi grid weight inputs based on selected radio grid options
        function updateMultiGridWeightInputs(questionIndex) {
            const selectedOptions = $(`.radio-grid-option[data-question-index="${questionIndex}"]:checked`);
            const weightsContainer = $(`#individualMultiGridWeights${questionIndex}`);
            const defaultWeight = $(`#defaultMultiGridWeight${questionIndex}`).val();

            // Clear existing weight inputs
            weightsContainer.empty();

            // Add weight inputs for each selected combination
            selectedOptions.each(function(index) {
                const combinationValue = $(this).val();
                const row = $(this).data('row');
                const column = $(this).data('column');

                const weightInput = `
                    <div class="mb-2 row">
                        <label class="col-sm-8 col-form-label">${combinationValue}</label>
                        <div class="col-sm-4">
                            <input type="number" class="form-control"
                                   name="multi_grid_weight_${index}"
                                   value="${defaultWeight}" min="1">
                        </div>
                    </div>
                `;

                weightsContainer.append(weightInput);
            });
        }

        // Function to add a new date range
        function addDateRange(questionIndex) {
            const container = $(`#dateRangesContainer${questionIndex}`);
            const rangeIndex = container.children().length;

            const dateRangeHtml = `
                <div class="range-item mb-3 p-3 border rounded">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">Start Date</label>
                            <input type="date" class="form-control" name="date_range_${rangeIndex}_start" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">End Date</label>
                            <input type="date" class="form-control" name="date_range_${rangeIndex}_end" required>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Weight</label>
                            <input type="number" class="form-control" name="date_range_${rangeIndex}_weight" value="1" min="1" required>
                        </div>
                        <div class="col-md-1 d-flex align-items-end">
                            <button type="button" class="btn btn-outline-danger btn-sm remove-range" title="Remove Range">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="form-text mt-2">
                        <i class="fas fa-info-circle me-1"></i>
                        Dates will be randomly generated within this range. Higher weight = higher chance of selection.
                    </div>
                </div>
            `;

            container.append(dateRangeHtml);
        }

        // Function to add a new time range
        function addTimeRange(questionIndex) {
            const container = $(`#timeRangesContainer${questionIndex}`);
            const rangeIndex = container.children().length;

            const timeRangeHtml = `
                <div class="range-item mb-3 p-3 border rounded">
                    <div class="row">
                        <div class="col-md-4">
                            <label class="form-label">Start Time</label>
                            <input type="time" class="form-control" name="time_range_${rangeIndex}_start" required>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">End Time</label>
                            <input type="time" class="form-control" name="time_range_${rangeIndex}_end" required>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">Weight</label>
                            <input type="number" class="form-control" name="time_range_${rangeIndex}_weight" value="1" min="1" required>
                        </div>
                        <div class="col-md-1 d-flex align-items-end">
                            <button type="button" class="btn btn-outline-danger btn-sm remove-range" title="Remove Range">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="form-text mt-2">
                        <i class="fas fa-info-circle me-1"></i>
                        Times will be randomly generated within this range. Higher weight = higher chance of selection.
                    </div>
                </div>
            `;

            container.append(timeRangeHtml);
        }

        // Function to add a new checkbox combination
        function addCheckboxCombination(questionIndex) {
            const container = $(`#checkboxCombinationsContainer${questionIndex}`);
            const combinationIndex = container.children().length;

            // Get available options from the question data
            const options = window.questionOptions[questionIndex] || [];

            // Generate options HTML
            let optionsHtml = '';
            options.forEach((option, index) => {
                optionsHtml += `
                    <div class="col-md-6 mb-2">
                        <div class="form-check">
                            <input class="form-check-input combination-option" type="checkbox"
                                   id="combo_${combinationIndex}_option_${index}_${questionIndex}"
                                   name="combination_${combinationIndex}_options"
                                   value="${option}"
                                   data-combination-index="${combinationIndex}"
                                   data-question-index="${questionIndex}">
                            <label class="form-check-label" for="combo_${combinationIndex}_option_${index}_${questionIndex}">
                                ${option}
                            </label>
                        </div>
                    </div>
                `;
            });

            const combinationHtml = `
                <div class="combination-item mb-3 p-3 border rounded">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">
                            <i class="fas fa-check-square text-primary me-2"></i>
                            Combination ${combinationIndex + 1}
                        </h6>
                        <button type="button" class="btn btn-outline-danger btn-sm remove-combination" title="Remove Combination">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Select options for this combination:</label>
                        <div class="row">
                            ${optionsHtml}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <label class="form-label">Combination Name (Optional)</label>
                            <input type="text" class="form-control"
                                   name="combination_${combinationIndex}_name"
                                   placeholder="e.g., 'Option1 + Option2'">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Weight</label>
                            <input type="number" class="form-control"
                                   name="combination_${combinationIndex}_weight"
                                   value="1" min="1" required>
                        </div>
                    </div>

                    <div class="form-text mt-2">
                        <i class="fas fa-info-circle me-1"></i>
                        Select multiple options to create a combination. Higher weight = higher chance of selection.
                    </div>
                </div>
            `;

            container.append(combinationHtml);
        }

        // Function to add a new checkbox grid combination
        function addCheckboxGridCombination(questionIndex) {
            const container = $(`#checkboxGridCombinationsContainer${questionIndex}`);
            const combinationIndex = container.children().length;

            // Get available grid data
            const gridData = window.questionGridData[questionIndex] || { rows: [], columns: [] };
            const { rows, columns } = gridData;

            // Generate grid options HTML
            let gridOptionsHtml = '';
            rows.forEach((row, rowIndex) => {
                columns.forEach((column, colIndex) => {
                    gridOptionsHtml += `
                        <div class="col-md-6 mb-2">
                            <div class="form-check">
                                <input class="form-check-input grid-combination-option" type="checkbox"
                                       id="grid_combo_${combinationIndex}_${rowIndex}_${colIndex}_${questionIndex}"
                                       name="grid_combination_${combinationIndex}_options"
                                       value="${row}: ${column}"
                                       data-row="${row}"
                                       data-column="${column}"
                                       data-combination-index="${combinationIndex}"
                                       data-question-index="${questionIndex}">
                                <label class="form-check-label" for="grid_combo_${combinationIndex}_${rowIndex}_${colIndex}_${questionIndex}">
                                    ${row}: ${column}
                                </label>
                            </div>
                        </div>
                    `;
                });
            });

            const combinationHtml = `
                <div class="combination-item mb-3 p-3 border rounded">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">
                            <i class="fas fa-table text-primary me-2"></i>
                            Grid Combination ${combinationIndex + 1}
                        </h6>
                        <button type="button" class="btn btn-outline-danger btn-sm remove-combination" title="Remove Combination">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Select grid options for this combination:</label>
                        <div class="row">
                            ${gridOptionsHtml}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <label class="form-label">Combination Name (Optional)</label>
                            <input type="text" class="form-control"
                                   name="grid_combination_${combinationIndex}_name"
                                   placeholder="e.g., 'Row1: Col1 + Row2: Col2'">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Weight</label>
                            <input type="number" class="form-control"
                                   name="grid_combination_${combinationIndex}_weight"
                                   value="1" min="1" required>
                        </div>
                    </div>

                    <div class="form-text mt-2">
                        <i class="fas fa-info-circle me-1"></i>
                        Select multiple grid options to create a combination. Higher weight = higher chance of selection.
                    </div>
                </div>
            `;

            container.append(combinationHtml);
        }

        // Function to add a new multiple choice grid combination
        function addMultiGridCombination(questionIndex) {
            const container = $(`#multiGridCombinationsContainer${questionIndex}`);
            const combinationIndex = container.children().length;

            // Get available grid data
            const gridData = window.questionGridData[questionIndex] || { rows: [], columns: [] };
            const { rows, columns } = gridData;

            // Generate grid options HTML for multiple choice (radio buttons per row)
            let gridOptionsHtml = '';
            rows.forEach((row, rowIndex) => {
                gridOptionsHtml += `
                    <div class="mb-3">
                        <label class="form-label"><strong>${row}:</strong></label>
                        <div class="row">
                `;
                columns.forEach((column, colIndex) => {
                    gridOptionsHtml += `
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input class="form-check-input multi-grid-combination-option" type="radio"
                                       name="multi_grid_combination_${combinationIndex}_row_${rowIndex}"
                                       id="multi_grid_combo_${combinationIndex}_${rowIndex}_${colIndex}_${questionIndex}"
                                       value="${row}: ${column}"
                                       data-row="${row}"
                                       data-column="${column}"
                                       data-combination-index="${combinationIndex}"
                                       data-question-index="${questionIndex}">
                                <label class="form-check-label" for="multi_grid_combo_${combinationIndex}_${rowIndex}_${colIndex}_${questionIndex}">
                                    ${column}
                                </label>
                            </div>
                        </div>
                    `;
                });
                gridOptionsHtml += `
                        </div>
                    </div>
                `;
            });

            const combinationHtml = `
                <div class="combination-item mb-3 p-3 border rounded">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">
                            <i class="fas fa-table text-primary me-2"></i>
                            Multi Grid Combination ${combinationIndex + 1}
                        </h6>
                        <button type="button" class="btn btn-outline-danger btn-sm remove-combination" title="Remove Combination">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Select one option per row for this combination:</label>
                        ${gridOptionsHtml}
                    </div>

                    <div class="row">
                        <div class="col-md-8">
                            <label class="form-label">Combination Name (Optional)</label>
                            <input type="text" class="form-control"
                                   name="multi_grid_combination_${combinationIndex}_name"
                                   placeholder="e.g., 'Row1: Col1, Row2: Col2'">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Weight</label>
                            <input type="number" class="form-control"
                                   name="multi_grid_combination_${combinationIndex}_weight"
                                   value="1" min="1" required>
                        </div>
                    </div>

                    <div class="form-text mt-2">
                        <i class="fas fa-info-circle me-1"></i>
                        Select one option per row to create a combination. Higher weight = higher chance of selection.
                    </div>
                </div>
            `;

            container.append(combinationHtml);
        }

        // Handle AI provider selection and load models
        $('.ai-provider-select').off('change').on('change', function() {
            const provider = $(this).val();
            const questionIndex = $(this).data('question-index');
            const modelSelect = $(this).closest('form').find('.ai-model-select');

            // Clear current options and show loading
            modelSelect.html('<option value="">Loading models...</option>');

            // Fetch models for the selected provider
            $.ajax({
                url: `/api/models/${provider}`,
                method: 'GET',
                success: function(response) {
                    if (response.success && response.models) {
                        // Clear loading option
                        modelSelect.empty();

                        // Add default option
                        modelSelect.append('<option value="">Select a model</option>');

                        // Add models to select
                        response.models.forEach(function(model) {
                            // Create model name with context length if available
                            let displayName = model.name;
                            if (model.context_length) {
                                displayName += ` (${Math.round(model.context_length / 1000)}k ctx)`;
                            }

                            // Add modality icons if available
                            const hasImage = model.input_modalities && model.input_modalities.includes('image');
                            if (hasImage) {
                                displayName = '🖼️ ' + displayName;
                            }

                            modelSelect.append(`<option value="${model.id}">${displayName}</option>`);
                        });
                    } else {
                        modelSelect.html('<option value="">No models available</option>');
                    }
                },
                error: function() {
                    modelSelect.html('<option value="">Error loading models</option>');
                }
            });
        });

        // Initialize models for default provider
        $('.ai-provider-select').each(function() {
            $(this).trigger('change');
        });

        // Generate responses for a single question
        $('.generate-for-question').off('click').on('click', function() {
            const questionIndex = $(this).data('question-index');

            // Find the active tab within this specific modal
            const modalId = 'generateModal' + questionIndex;
            const activeTab = $('#' + modalId + ' .nav-link.active').attr('id');
            let formId;

            if (activeTab && activeTab.includes('manual')) {
                formId = 'manualForm' + questionIndex;
            } else if (activeTab && activeTab.includes('ai')) {
                formId = 'aiForm' + questionIndex;
            } else if (activeTab && activeTab.includes('auto')) {
                formId = 'autoForm' + questionIndex;
            } else {
                // Default to manual form if no active tab is found
                formId = 'manualForm' + questionIndex;
            }

            // Check if the form exists
            const form = document.getElementById(formId);
            if (!form) {
                alert('Error: Form not found. Please try again or refresh the page.');
                return;
            }

            // Validate form
            if (!validateForm(form)) {
                return;
            }

            const formData = new FormData(form);
            formData.append('form_id', '{{ form_id }}');
            formData.append('question_index', questionIndex);

            // Show loading state
            const button = $(this);
            const originalText = button.html();
            button.html('<i class="fas fa-spinner fa-spin me-1"></i>Processing...');
            button.prop('disabled', true);

            // Special handling for checkbox combinations
            if (formId.includes('manualForm') && form.querySelector('input[name="method"]').value === 'manual_checkbox_combinations') {
                const combinationItems = form.querySelectorAll('.combination-item');

                if (combinationItems.length === 0) {
                    alert('Please add at least one checkbox combination.');
                    button.html(originalText);
                    button.prop('disabled', false);
                    return;
                }

                // Create a special data structure for checkbox combinations
                const combinationsData = [];

                combinationItems.forEach((item, combinationIndex) => {
                    const selectedOptions = item.querySelectorAll('.combination-option:checked');
                    const nameInput = item.querySelector(`input[name="combination_${combinationIndex}_name"]`);
                    const weightInput = item.querySelector(`input[name="combination_${combinationIndex}_weight"]`);

                    if (selectedOptions.length === 0) {
                        alert(`Please select at least one option for Combination ${combinationIndex + 1}.`);
                        button.html(originalText);
                        button.prop('disabled', false);
                        return;
                    }

                    const options = [];
                    selectedOptions.forEach(option => {
                        options.push(option.value);
                    });

                    const combinationName = nameInput ? nameInput.value.trim() : '';
                    const weight = weightInput ? parseInt(weightInput.value) || 1 : 1;

                    combinationsData.push({
                        name: combinationName || `Combination ${combinationIndex + 1}`,
                        options: options,
                        weight: weight
                    });
                });

                // Add the combinations data to the form data
                formData.append('checkbox_combinations', JSON.stringify(combinationsData));
            }

            // Special handling for checkbox grid combinations
            if (formId.includes('manualForm') && form.querySelector('input[name="method"]').value === 'manual_checkbox_grid_combinations') {
                const combinationItems = form.querySelectorAll('.combination-item');

                if (combinationItems.length === 0) {
                    alert('Please add at least one checkbox grid combination.');
                    button.html(originalText);
                    button.prop('disabled', false);
                    return;
                }

                // Create a special data structure for checkbox grid combinations
                const gridCombinationsData = [];

                combinationItems.forEach((item, combinationIndex) => {
                    const selectedOptions = item.querySelectorAll('.grid-combination-option:checked');
                    const nameInput = item.querySelector(`input[name="grid_combination_${combinationIndex}_name"]`);
                    const weightInput = item.querySelector(`input[name="grid_combination_${combinationIndex}_weight"]`);

                    if (selectedOptions.length === 0) {
                        alert(`Please select at least one grid option for Grid Combination ${combinationIndex + 1}.`);
                        button.html(originalText);
                        button.prop('disabled', false);
                        return;
                    }

                    const options = [];
                    selectedOptions.forEach(option => {
                        options.push({
                            row: option.dataset.row,
                            column: option.dataset.column,
                            value: option.value
                        });
                    });

                    const combinationName = nameInput ? nameInput.value.trim() : '';
                    const weight = weightInput ? parseInt(weightInput.value) || 1 : 1;

                    gridCombinationsData.push({
                        name: combinationName || `Grid Combination ${combinationIndex + 1}`,
                        options: options,
                        weight: weight
                    });
                });

                // Add the grid combinations data to the form data
                formData.append('checkbox_grid_combinations', JSON.stringify(gridCombinationsData));
            }

            // Special handling for multiple choice grid combinations
            if (formId.includes('manualForm') && form.querySelector('input[name="method"]').value === 'manual_multiple_choice_grid_combinations') {
                const combinationItems = form.querySelectorAll('.combination-item');

                if (combinationItems.length === 0) {
                    alert('Please add at least one multiple choice grid combination.');
                    button.html(originalText);
                    button.prop('disabled', false);
                    return;
                }

                // Create a special data structure for multiple choice grid combinations
                const multiGridCombinationsData = [];

                combinationItems.forEach((item, combinationIndex) => {
                    const selectedOptions = item.querySelectorAll('.multi-grid-combination-option:checked');
                    const nameInput = item.querySelector(`input[name="multi_grid_combination_${combinationIndex}_name"]`);
                    const weightInput = item.querySelector(`input[name="multi_grid_combination_${combinationIndex}_weight"]`);

                    if (selectedOptions.length === 0) {
                        alert(`Please select at least one option per row for Multi Grid Combination ${combinationIndex + 1}.`);
                        button.html(originalText);
                        button.prop('disabled', false);
                        return;
                    }

                    const options = [];
                    selectedOptions.forEach(option => {
                        options.push({
                            row: option.dataset.row,
                            column: option.dataset.column,
                            value: option.value
                        });
                    });

                    const combinationName = nameInput ? nameInput.value.trim() : '';
                    const weight = weightInput ? parseInt(weightInput.value) || 1 : 1;

                    multiGridCombinationsData.push({
                        name: combinationName || `Multi Grid Combination ${combinationIndex + 1}`,
                        options: options,
                        weight: weight
                    });
                });

                // Add the multi grid combinations data to the form data
                formData.append('multi_grid_combinations', JSON.stringify(multiGridCombinationsData));
            }

            // Special handling for checkbox grid questions
            if (formId.includes('manualForm') && form.querySelector('input[name="method"]').value === 'manual_checkbox_grid') {
                const useWeights = form.querySelector('input[name="use_grid_weights"]').checked;
                const selectedOptions = form.querySelectorAll('.checkbox-grid-option:checked');

                if (selectedOptions.length === 0) {
                    alert('Please select at least one combination for the checkbox grid question.');
                    button.html(originalText);
                    button.prop('disabled', false);
                    return;
                }

                // Create a special data structure for checkbox grid responses
                const gridData = [];

                selectedOptions.forEach((option, index) => {
                    const combinationValue = option.value;
                    const row = option.dataset.row;
                    const column = option.dataset.column;
                    let weight = 1;

                    if (useWeights) {
                        const weightInput = form.querySelector(`input[name="grid_weight_${index}"]`);
                        if (weightInput) {
                            weight = parseInt(weightInput.value) || 1;
                        }
                    }

                    gridData.push({
                        row: row,
                        column: column,
                        combination: combinationValue,
                        weight: weight
                    });
                });

                // Add the grid data to the form data
                formData.append('grid_data', JSON.stringify(gridData));
            }

            // Special handling for multiple choice grid questions
            if (formId.includes('manualForm') && form.querySelector('input[name="method"]').value === 'manual_multiple_choice_grid') {
                const useWeights = form.querySelector('input[name="use_multi_grid_weights"]').checked;
                const selectedOptions = form.querySelectorAll('.radio-grid-option:checked');

                if (selectedOptions.length === 0) {
                    alert('Please select at least one combination for the multiple choice grid question.');
                    button.html(originalText);
                    button.prop('disabled', false);
                    return;
                }

                // Create a special data structure for multiple choice grid responses
                const multiGridData = [];

                selectedOptions.forEach((option, index) => {
                    const combinationValue = option.value;
                    const row = option.dataset.row;
                    const column = option.dataset.column;
                    let weight = 1;

                    if (useWeights) {
                        const weightInput = form.querySelector(`input[name="multi_grid_weight_${index}"]`);
                        if (weightInput) {
                            weight = parseInt(weightInput.value) || 1;
                        }
                    }

                    multiGridData.push({
                        row: row,
                        column: column,
                        combination: combinationValue,
                        weight: weight
                    });
                });

                // Add the multi grid data to the form data
                formData.append('multi_grid_data', JSON.stringify(multiGridData));
            }

            // Special handling for date questions
            if (formId.includes('manualForm') && form.querySelector('input[name="method"]').value === 'manual_date') {
                const dateRanges = [];
                const rangeItems = form.querySelectorAll('.range-item');

                if (rangeItems.length === 0) {
                    alert('Please add at least one date range.');
                    button.html(originalText);
                    button.prop('disabled', false);
                    return;
                }

                rangeItems.forEach((item, index) => {
                    const startDate = item.querySelector(`input[name="date_range_${index}_start"]`).value;
                    const endDate = item.querySelector(`input[name="date_range_${index}_end"]`).value;
                    const weight = parseInt(item.querySelector(`input[name="date_range_${index}_weight"]`).value) || 1;

                    if (!startDate || !endDate) {
                        alert('Please fill in all date range fields.');
                        button.html(originalText);
                        button.prop('disabled', false);
                        return;
                    }

                    if (new Date(startDate) > new Date(endDate)) {
                        alert('Start date must be before or equal to end date.');
                        button.html(originalText);
                        button.prop('disabled', false);
                        return;
                    }

                    dateRanges.push({
                        start: startDate,
                        end: endDate,
                        weight: weight
                    });
                });

                // Add the date ranges data to the form data
                formData.append('date_ranges', JSON.stringify(dateRanges));
            }

            // Special handling for time questions
            if (formId.includes('manualForm') && form.querySelector('input[name="method"]').value === 'manual_time') {
                const timeRanges = [];
                const rangeItems = form.querySelectorAll('.range-item');

                if (rangeItems.length === 0) {
                    alert('Please add at least one time range.');
                    button.html(originalText);
                    button.prop('disabled', false);
                    return;
                }

                rangeItems.forEach((item, index) => {
                    const startTime = item.querySelector(`input[name="time_range_${index}_start"]`).value;
                    const endTime = item.querySelector(`input[name="time_range_${index}_end"]`).value;
                    const weight = parseInt(item.querySelector(`input[name="time_range_${index}_weight"]`).value) || 1;

                    if (!startTime || !endTime) {
                        alert('Please fill in all time range fields.');
                        button.html(originalText);
                        button.prop('disabled', false);
                        return;
                    }

                    timeRanges.push({
                        start: startTime,
                        end: endTime,
                        weight: weight
                    });
                });

                // Add the time ranges data to the form data
                formData.append('time_ranges', JSON.stringify(timeRanges));
            }

            // Submit the form data
            $.ajax({
                url: '/api/generate_for_question',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    // Restore button state
                    button.html(originalText);
                    button.prop('disabled', false);

                    alert('Responses generated successfully!');

                    // Properly close the modal using our utility function
                    const modalId = 'generateModal' + questionIndex;
                    AppUtils.safelyHideModal(modalId);

                    // Reload the page after the modal is fully hidden
                    $('#' + modalId).on('hidden.bs.modal', function() {
                        location.reload();
                    });
                },
                error: function(xhr) {
                    // Restore button state
                    button.html(originalText);
                    button.prop('disabled', false);

                    alert('Error: ' + (xhr.responseJSON ? xhr.responseJSON.error : 'Unknown error'));
                }
            });
        });

        /**
         * Validate form before submission
         */
        function validateForm(form) {
            // Check for required fields
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            // Reset previous validation
            form.querySelectorAll('.is-invalid').forEach(el => {
                el.classList.remove('is-invalid');
            });

            // Check each required field
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;

                    // Add error message if not already present
                    const parent = field.parentElement;
                    if (!parent.querySelector('.invalid-feedback')) {
                        const feedback = document.createElement('div');
                        feedback.className = 'invalid-feedback';
                        feedback.textContent = 'This field is required';
                        parent.appendChild(feedback);
                    }
                }
            });

            // Special validation for API key in Gemini tabs
            if (form.id.includes('geminiForm') || form.id.includes('autoForm')) {
                const apiKeyField = form.querySelector('[name="api_key"]');
                if (apiKeyField && !apiKeyField.value.trim()) {
                    apiKeyField.classList.add('is-invalid');
                    isValid = false;

                    // Add error message if not already present
                    const parent = apiKeyField.parentElement;
                    if (!parent.querySelector('.invalid-feedback')) {
                        const feedback = document.createElement('div');
                        feedback.className = 'invalid-feedback';
                        feedback.textContent = 'API key is required for Gemini-powered generation';
                        parent.appendChild(feedback);
                    }
                }
            }

            if (!isValid) {
                // Scroll to the first invalid field
                const firstInvalid = form.querySelector('.is-invalid');
                if (firstInvalid) {
                    firstInvalid.focus();
                }
            }

            return isValid;
        }


    });
</script>

<!-- Add CSS to prevent transitions during initialization and fix hover transforms -->
<style>
.no-transitions * {
    transition: none !important;
    animation: none !important;
}

.modal.fade.show {
    transition: opacity 0.15s linear;
}

.modal-backdrop.fade {
    transition: opacity 0.15s linear;
}

/* CRITICAL FIX: Disable transform hover effects that cause modal flashing */
.card:hover,
.btn:hover,
.badge:hover,
.hover-lift:hover {
    transform: none !important;
}

/* Keep other hover effects but remove transforms */
.card:hover {
    box-shadow: var(--shadow-lg) !important;
    border-color: var(--border-light) !important;
    transform: none !important; /* This is the key fix */
}

.btn-primary:hover,
.btn-success:hover,
.btn-warning:hover,
.btn-danger:hover,
.btn-info:hover,
.btn-secondary:hover {
    transform: none !important; /* Disable transform on button hover */
}

.badge:hover {
    box-shadow: var(--shadow-sm) !important;
    transform: none !important; /* Disable transform on badge hover */
}

/* Prevent any hover-triggered transforms or transitions on modal elements */
.modal *, .modal-backdrop * {
    transition-property: opacity !important;
    transition-duration: 0.15s !important;
}

/* Prevent cursor-related visual glitches */
.modal {
    cursor: default;
}

.modal-content {
    cursor: default;
}

/* Improve modal performance */
.modal-backdrop {
    will-change: opacity;
}

.modal.fade {
    will-change: transform, opacity;
}

/* Force stable positioning for modals */
.modal.show {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* Prevent backdrop from interfering with cursor detection */
.modal-backdrop {
    pointer-events: none !important;
}

.modal-backdrop.show {
    pointer-events: auto !important;
}

/* Fix for modal tabs text visibility */
.modal .nav-tabs .nav-link {
    color: var(--text-secondary) !important;
}

.modal .nav-tabs .nav-link:hover {
    color: var(--text-primary) !important;
}

.modal .nav-tabs .nav-link.active {
    color: var(--accent-primary) !important;
    font-weight: 600;
}

.modal .tab-content {
    color: var(--text-primary) !important;
}

/* Checkbox options styling */
.checkbox-options-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    padding: 0.5rem;
    margin-bottom: 1rem;
}

.checkbox-options-container .form-check {
    padding: 0.5rem;
    border-radius: 0.25rem;
    transition: background-color 0.15s ease-in-out;
}

.checkbox-options-container .form-check:hover {
    background-color: var(--bg-light);
}

.checkbox-options-container .form-check-input:checked + .form-check-label {
    font-weight: 600;
    color: var(--accent-primary);
}

.individual-weights-container,
.individual-grid-weights-container,
.individual-multi-grid-weights-container {
    margin-top: 1rem;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    background-color: var(--bg-light);
}

.individual-weights-container .row:hover,
.individual-grid-weights-container .row:hover,
.individual-multi-grid-weights-container .row:hover {
    background-color: var(--bg-hover);
    border-radius: 0.25rem;
}

/* Grid question styling */
.table-responsive .form-check-input {
    margin: 0;
}

.table-responsive .form-check {
    margin-bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 2rem;
}

.table-responsive td {
    vertical-align: middle;
}

.table-responsive th {
    text-align: center;
    vertical-align: middle;
    font-weight: 600;
    background-color: var(--bg-light);
}

.table-responsive tbody tr:hover {
    background-color: var(--bg-hover);
}

.checkbox-grid-option:checked + label,
.radio-grid-option:checked + label {
    background-color: var(--accent-primary);
    color: white;
    border-radius: 0.25rem;
    padding: 0.25rem;
}

/* Date and Time Range Styling */
.date-ranges-container,
.time-ranges-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 0.25rem;
    padding: 0.5rem;
    margin-bottom: 1rem;
    background-color: var(--bg-light);
}

.range-item {
    background-color: var(--bg-white);
    transition: all 0.15s ease-in-out;
}

.range-item:hover {
    background-color: var(--bg-hover);
    border-color: var(--accent-primary) !important;
}

.range-item .btn-outline-danger {
    border-color: #dc3545;
    color: #dc3545;
}

.range-item .btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.add-date-range,
.add-time-range {
    transition: all 0.15s ease-in-out;
}

.add-date-range:hover,
.add-time-range:hover {
    background-color: var(--accent-primary);
    border-color: var(--accent-primary);
    color: white;
}
</style>
{% endblock %}
