"""
Routes Module for Google Form AutoFill Web Interface

This module defines the routes for the web interface.
"""

import os
import json
import re
import time
import sys
from flask import render_template, request, redirect, url_for, flash, session, jsonify, send_from_directory
from werkzeug.utils import secure_filename

# Add parent directory to path to import modules from parent directory
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Import core modules from parent directory
from form_enhanced import EnhancedFormParser
from gemini_client import GeminiClient
from response_generator import ResponseGenerator
from response_storage import FormResponseManager
from submission_manager import SubmissionManager

# Import web-specific modules using relative imports
from .forms import LoadFormForm, GenerateResponsesForm, SubmitFormForm, ApiKeyForm, CustomerFormConfigForm
from .utils import get_saved_forms, allowed_file


def register_routes(app):
    """Register routes with the Flask application"""

    @app.route('/')
    def index():
        """Home page"""
        # Get list of saved forms
        saved_forms = get_saved_forms()
        return render_template('index.html', saved_forms=saved_forms)

    @app.route('/load', methods=['GET', 'POST'])
    def load_form():
        """Load a form from URL"""
        form = LoadFormForm()

        if form.validate_on_submit():
            url = form.url.data

            # Load form
            parser = EnhancedFormParser(url)
            if parser.load_form():
                # Extract form details
                session['form_url'] = url # Keep for potential immediate use, though primary data is now stored
                form_id = parser.get_form_id()
                form_title = parser.get_form_title()
                form_description = parser.get_form_description()
                questions = parser.get_questions()

                session['form_id'] = form_id # Keep for redirect
                session['form_title'] = form_title # Keep for quick display if needed before full load

                manager = FormResponseManager()
                existing_form_data = manager.load_form_data(form_id)

                if existing_form_data:
                    # Update existing form data
                    existing_form_data['form_title'] = form_title
                    existing_form_data['form_description'] = form_description
                    existing_form_data['questions'] = questions # Overwrites questions, assuming parser has latest
                    existing_form_data['form_url'] = url
                    existing_form_data['last_modified_at'] = time.time()
                    # Responses and created_at are preserved
                    manager.save_form_data(form_id, existing_form_data)
                    flash('Form data re-loaded and updated. Existing responses have been preserved.', 'info')
                else:
                    # Initialize and save new form data
                    initialized_data = {
                        "form_id": form_id,
                        "form_title": form_title,
                        "form_description": form_description,
                        "questions": questions,
                        "responses": {}, # Start with empty responses for a new form
                        "form_url": url,
                        "created_at": time.time(),
                        "last_modified_at": time.time()
                    }
                    manager.save_form_data(form_id, initialized_data)
                    flash('Form loaded and saved successfully!', 'success')

                # Redirect to form details page
                return redirect(url_for('form_details', form_id=form_id))
            else:
                flash('Failed to load form. Please check the URL and try again.', 'danger')

        return render_template('load_form.html', form=form)

    @app.route('/form/<form_id>')
    def form_details(form_id):
        """Display form details"""
        # Load form data
        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)

        if not form_data:
            flash('Form not found. Please try loading it again.', 'danger')
            return redirect(url_for('index'))

        # Get API key from environment
        api_key = os.environ.get('GEMINI_API_KEY', '')

        return render_template('form_details.html', form_data=form_data, form_id=form_id, api_key=api_key)

    @app.route('/generate/<form_id>', methods=['GET', 'POST'])
    def generate_responses(form_id):
        """Generate responses for a form"""
        form = GenerateResponsesForm()

        # Load form data
        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)

        if not form_data:
            flash('Form not found.', 'danger')
            return redirect(url_for('index'))

        if form.validate_on_submit():
            method = form.method.data
            sample_count = form.sample_count.data
            api_key = form.api_key.data

            # Handle file upload
            examples = None
            if form.examples_file.data:
                file = form.examples_file.data
                if file and allowed_file(file.filename):
                    filename = secure_filename(file.filename)
                    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                    file.save(filepath)

                    # Load examples from file
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            examples = json.load(f)
                    except Exception as e:
                        flash(f'Failed to load examples: {e}', 'danger')
                        return redirect(url_for('generate_responses', form_id=form_id))

            # Initialize Gemini client if needed
            gemini = None
            if method != 'manual':
                if not api_key:
                    flash('Gemini API key is required for this method.', 'danger')
                    return redirect(url_for('generate_responses', form_id=form_id))

                try:
                    gemini = GeminiClient(api_key)
                except Exception as e:
                    flash(f'Failed to initialize Gemini client: {e}', 'danger')
                    return redirect(url_for('generate_responses', form_id=form_id))

            # Generate responses
            generator = ResponseGenerator(gemini)

            # Create a parser instance with the form URL
            form_url = form_data.get('form_url', '')
            if not form_url:
                # Try to get URL from session
                form_url = session.get('form_url', '')

            if not form_url:
                flash('Form URL not found. Please reload the form.', 'danger')
                return redirect(url_for('form_details', form_id=form_id))

            parser = EnhancedFormParser(form_url)
            parser.load_form()

            # If Gemini is used but no examples provided, and batch_optimized is not selected,
            # use batch_optimized for efficiency
            if method != 'manual' and method != 'batch_optimized' and not examples:
                flash(f'Using batch optimized method for efficiency. Original method: {method}', 'info')
                method = 'batch_optimized'

            # Generate responses
            result = generator.generate_responses_for_form(parser, method, sample_count, examples)

            flash(result, 'success')
            return redirect(url_for('review_responses', form_id=form_id))

        # Pre-fill API key from environment if not already set
        if not form.api_key.data:
            form.api_key.data = os.environ.get('GEMINI_API_KEY', '')

        return render_template('generate_responses.html', form=form, form_data=form_data, form_id=form_id)

    @app.route('/review/<form_id>')
    def review_responses(form_id):
        """Review generated responses"""
        # Load form data
        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)

        if not form_data:
            flash('Form not found.', 'danger')
            return redirect(url_for('index'))

        return render_template('review_responses.html', form_data=form_data, form_id=form_id)

    @app.route('/edit/<form_id>/<question_index>/<response_index>', methods=['GET', 'POST'])
    def edit_response(form_id, question_index, response_index):
        """Edit a response"""
        # Load form data
        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)

        if not form_data:
            flash('Form not found.', 'danger')
            return redirect(url_for('index'))

        # Get question and response
        try:
            question_index = int(question_index)
            response_index = int(response_index)

            if question_index < 1 or question_index > len(form_data.get('questions', [])):
                flash('Invalid question index.', 'danger')
                return redirect(url_for('review_responses', form_id=form_id))

            question = form_data['questions'][question_index - 1]
            question_id = question['id']

            responses = form_data.get('responses', {}).get(question_id, [])
            if response_index < 1 or response_index > len(responses):
                flash('Invalid response index.', 'danger')
                return redirect(url_for('review_responses', form_id=form_id))

            response = responses[response_index - 1]
        except Exception as e:
            flash(f'Error: {e}', 'danger')
            return redirect(url_for('review_responses', form_id=form_id))

        if request.method == 'POST':
            # Update response
            new_text = request.form.get('text', '')
            new_weight = request.form.get('weight', '')

            try:
                new_weight = int(new_weight) if new_weight else None
            except ValueError:
                flash('Weight must be a number.', 'danger')
                return redirect(url_for('edit_response', form_id=form_id, question_index=question_index, response_index=response_index))

            success = manager.update_response(form_id, question_id, response_index - 1, new_text, new_weight)

            if success:
                flash('Response updated successfully.', 'success')
            else:
                flash('Failed to update response.', 'danger')

            return redirect(url_for('review_responses', form_id=form_id))

        return render_template('edit_response.html', form_data=form_data, form_id=form_id,
                              question=question, response=response,
                              question_index=question_index, response_index=response_index)

    @app.route('/submit/<form_id>', methods=['GET', 'POST'])
    def submit_form(form_id):
        """Submit form responses"""
        form = SubmitFormForm()

        # Load form data
        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)

        if not form_data:
            flash('Form not found.', 'danger')
            return redirect(url_for('index'))

        if form.validate_on_submit():
            count = form.count.data
            delay_min = form.delay_min.data  # Now in milliseconds
            delay_max = form.delay_max.data  # Now in milliseconds
            max_workers = form.max_workers.data
            form_url = form.url.data

            # Store submission parameters in session
            session['submission_form_id'] = form_id
            session['submission_form_url'] = form_url
            session['submission_count'] = count
            session['submission_delay_min'] = delay_min
            session['submission_delay_max'] = delay_max
            session['submission_max_workers'] = max_workers

            return redirect(url_for('submission_progress'))

        # Pre-fill form URL if available
        if not form.url.data:
            form.url.data = form_data.get('form_url', '')

        return render_template('submit_form.html', form=form, form_data=form_data, form_id=form_id)

    @app.route('/submission_progress')
    def submission_progress():
        """Display submission progress"""
        # Get submission parameters from session
        form_id = session.get('submission_form_id')
        form_url = session.get('submission_form_url')
        count = session.get('submission_count')
        delay_min = session.get('submission_delay_min')
        delay_max = session.get('submission_delay_max')
        max_workers = session.get('submission_max_workers', 5)

        if not all([form_id, form_url, count, delay_min, delay_max]):
            flash('Submission parameters not found.', 'danger')
            return redirect(url_for('index'))

        return render_template('submission_progress.html',
                              form_id=form_id, form_url=form_url,
                              count=count, delay_min=delay_min, delay_max=delay_max,
                              max_workers=max_workers)

    @app.route('/config', methods=['GET', 'POST'])
    def config():
        """Configuration page"""
        # Import ConfigManager and Logger
        from config_manager import ConfigManager
        from logger import get_logger

        # Initialize config manager and logger
        config_manager = ConfigManager()
        logger = get_logger()

        form = ApiKeyForm()

        if form.validate_on_submit():
            # Process AI provider configuration
            default_provider = form.default_provider.data

            # Process Gemini configuration
            gemini_enabled = form.gemini_enabled.data == 'true'
            gemini_api_keys = [key.strip() for key in form.gemini_api_keys.data.split(',') if key.strip()]
            gemini_default_model = form.gemini_default_model.data
            gemini_key_strategy = form.gemini_key_strategy.data

            # Process OpenRouter configuration
            openrouter_enabled = form.openrouter_enabled.data == 'true'
            openrouter_api_keys = [key.strip() for key in form.openrouter_api_keys.data.split(',') if key.strip()]
            openrouter_default_model = form.openrouter_default_model.data
            openrouter_key_strategy = form.openrouter_key_strategy.data

            # Build configuration updates from form data
            config_updates = {
                "customer_plugin_enabled": form.customer_plugin_enabled.data == 'true',
                "max_responses_per_customer": form.max_responses_per_customer.data,
                "features": {
                    "ai_generation": form.ai_generation_enabled.data == 'true',
                    "manual_review": form.manual_review_enabled.data == 'true',
                    "batch_submission": form.batch_submission_enabled.data == 'true'
                },
                "logging": {
                    "level": form.log_level.data,
                    "file_enabled": form.file_logging_enabled.data == 'true',
                    "console_enabled": form.console_logging_enabled.data == 'true'
                },
                "ai_providers": {
                    "gemini": {
                        "enabled": gemini_enabled,
                        "is_default": default_provider == "gemini",
                        "api_keys": gemini_api_keys,
                        "default_model": gemini_default_model,
                        "key_strategy": gemini_key_strategy
                    },
                    "openrouter": {
                        "enabled": openrouter_enabled,
                        "is_default": default_provider == "openrouter",
                        "api_keys": openrouter_api_keys,
                        "default_model": openrouter_default_model,
                        "key_strategy": openrouter_key_strategy
                    }
                }
            }

            # Update configuration
            config_manager.update_config(config_updates)

            # Update logger configuration
            logger.update_config()

            # Log configuration update
            logger.info("Configuration updated successfully")

            flash('Configuration saved successfully.', 'success')
            return redirect(url_for('index'))

        # Pre-fill form with current configuration
        if not form.is_submitted():
            # Get current configuration
            config = config_manager.get_config()

            # Get AI provider configuration
            ai_providers = config.get('ai_providers', {})

            # Set default provider
            for provider_name, provider_config in ai_providers.items():
                if provider_config.get('is_default', False):
                    form.default_provider.data = provider_name
                    break

            # Pre-fill Gemini configuration
            gemini_config = ai_providers.get('gemini', {})
            form.gemini_enabled.data = 'true' if gemini_config.get('enabled', True) else 'false'
            form.gemini_api_keys.data = ', '.join(gemini_config.get('api_keys', []))
            form.gemini_default_model.data = gemini_config.get('default_model', 'gemini-2.0-flash-lite')
            form.gemini_key_strategy.data = gemini_config.get('key_strategy', 'round_robin')

            # Pre-fill OpenRouter configuration
            openrouter_config = ai_providers.get('openrouter', {})
            form.openrouter_enabled.data = 'true' if openrouter_config.get('enabled', True) else 'false'
            form.openrouter_api_keys.data = ', '.join(openrouter_config.get('api_keys', []))
            form.openrouter_default_model.data = openrouter_config.get('default_model', 'openai/gpt-3.5-turbo')
            form.openrouter_key_strategy.data = openrouter_config.get('key_strategy', 'round_robin')

            # Pre-fill customer plugin settings
            form.customer_plugin_enabled.data = 'true' if config.get('customer_plugin_enabled', True) else 'false'
            form.max_responses_per_customer.data = config.get('max_responses_per_customer', 100)

            # Pre-fill feature toggles
            features = config.get('features', {})
            form.ai_generation_enabled.data = 'true' if features.get('ai_generation', True) else 'false'
            form.manual_review_enabled.data = 'true' if features.get('manual_review', True) else 'false'
            form.batch_submission_enabled.data = 'true' if features.get('batch_submission', True) else 'false'

            # Pre-fill logging settings
            logging_config = config.get('logging', {})
            form.log_level.data = logging_config.get('level', 'INFO')
            form.file_logging_enabled.data = 'true' if logging_config.get('file_enabled', True) else 'false'
            form.console_logging_enabled.data = 'true' if logging_config.get('console_enabled', True) else 'false'

        return render_template('config.html', form=form)

    @app.route('/uploads/<filename>')
    def uploaded_file(filename):
        """Serve uploaded files"""
        return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

    @app.route('/customer/form/<form_id>', methods=['GET', 'POST'])
    def customer_form(form_id):
        """Customer-facing interface for specifying form response requirements"""
        # Load form data
        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)

        if not form_data:
            flash('Form not found. Please try loading it again.', 'danger')
            return redirect(url_for('index'))

        # Initialize the form
        form = CustomerFormConfigForm()

        # Pre-populate form with existing customer configuration if it exists
        if 'customer_config' in form_data and request.method == 'GET':
            customer_config = form_data['customer_config']
            form.total_responses.data = customer_config.get('total_responses', 10)

        if form.validate_on_submit():
            # Save the customer configuration
            total_responses = form.total_responses.data

            # Create a customer configuration object
            customer_config = {
                'total_responses': total_responses,
                'questions': {},
                'created_at': time.time()
            }

            # Process each question's configuration
            for question in form_data.get('questions', []):
                question_id = str(question['id'])
                question_type = question.get('type')

                # Get configuration for this question
                if question_type in ['multiple_choice', 'dropdown', 'checkboxes', 'linear_scale', 'rating']:
                    # Option-based questions use percentage sliders
                    options_data = {}
                    for option in question.get('options', []):
                        field_name = f'question_{question_id}_option_{option.replace(" ", "_")}'
                        if field_name in request.form:
                            options_data[option] = int(request.form.get(field_name, 0))

                    customer_config['questions'][question_id] = {
                        'type': question_type,
                        'options': options_data
                    }
                else:
                    # Text-based questions
                    examples = []
                    for i in range(1, 4):  # Get up to 3 examples
                        example_field = f'question_{question_id}_example_{i}'
                        if example_field in request.form and request.form.get(example_field).strip():
                            examples.append(request.form.get(example_field))

                    description = request.form.get(f'question_{question_id}_description', '')

                    customer_config['questions'][question_id] = {
                        'type': question_type,
                        'description': description,
                        'examples': examples
                    }

            # Save the configuration to the form data
            form_data['customer_config'] = customer_config
            manager.save_form_data(form_id, form_data)

            # Redirect to the review page
            flash('Form configuration saved successfully!', 'success')
            return redirect(url_for('customer_review', form_id=form_id))

        return render_template('customer_form.html', form=form, form_data=form_data, form_id=form_id)

    @app.route('/customer/form/wizard/<form_id>', methods=['GET', 'POST'])
    def customer_form_wizard(form_id):
        """Customer-facing wizard interface for specifying form response requirements"""
        # Load form data
        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)

        if not form_data:
            flash('Form not found. Please try loading it again.', 'danger')
            return redirect(url_for('index'))

        # Initialize the form
        form = CustomerFormConfigForm()

        # Pre-populate form with existing customer configuration if it exists
        if 'customer_config' in form_data and request.method == 'GET':
            customer_config = form_data['customer_config']
            form.total_responses.data = customer_config.get('total_responses', 10)

        if form.validate_on_submit():
            # Save the customer configuration (same logic as regular customer_form)
            total_responses = form.total_responses.data

            # Create a customer configuration object
            customer_config = {
                'total_responses': total_responses,
                'questions': {},
                'created_at': time.time()
            }

            # Process each question's configuration
            for question in form_data.get('questions', []):
                question_id = str(question['id'])
                question_type = question.get('type')

                # Get configuration for this question
                if question_type in ['multiple_choice', 'dropdown', 'checkboxes', 'linear_scale', 'rating']:
                    # Option-based questions use percentage sliders
                    options_data = {}
                    for option in question.get('options', []):
                        field_name = f'question_{question_id}_option_{option.replace(" ", "_")}'
                        if field_name in request.form:
                            options_data[option] = int(request.form.get(field_name, 0))

                    customer_config['questions'][question_id] = {
                        'type': question_type,
                        'options': options_data
                    }
                else:
                    # Text-based questions
                    examples = []
                    for i in range(1, 4):  # Get up to 3 examples
                        example_field = f'question_{question_id}_example_{i}'
                        if example_field in request.form and request.form.get(example_field).strip():
                            examples.append(request.form.get(example_field))

                    description = request.form.get(f'question_{question_id}_description', '')

                    customer_config['questions'][question_id] = {
                        'type': question_type,
                        'description': description,
                        'examples': examples
                    }

            # Save the configuration to the form data
            form_data['customer_config'] = customer_config
            manager.save_form_data(form_id, form_data)

            # Redirect to the review page
            flash('Form configuration saved successfully!', 'success')
            return redirect(url_for('customer_review', form_id=form_id))

        return render_template('customer_form_wizard.html', form=form, form_data=form_data, form_id=form_id)

    @app.route('/customer/review/<form_id>')
    def customer_review(form_id):
        """Review page for customer form configuration"""
        # Load form data
        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)

        if not form_data:
            flash('Form not found. Please try loading it again.', 'danger')
            return redirect(url_for('index'))

        # Check if customer configuration exists
        if 'customer_config' not in form_data:
            flash('No configuration found. Please configure the form first.', 'warning')
            return redirect(url_for('customer_form', form_id=form_id))

        return render_template('customer_review.html', form_data=form_data, form_id=form_id)

    @app.route('/customer/share/<form_id>')
    def customer_share(form_id):
        """Generate shareable link for customer form configuration"""
        # Load form data
        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)

        if not form_data:
            flash('Form not found. Please try loading it again.', 'danger')
            return redirect(url_for('index'))

        # Check if customer configuration exists
        if 'customer_config' not in form_data:
            flash('No configuration found. Please configure the form first.', 'warning')
            return redirect(url_for('customer_form', form_id=form_id))

        # Generate a unique share ID if not already present
        if 'share_id' not in form_data.get('customer_config', {}):
            import uuid
            share_id = str(uuid.uuid4())
            form_data['customer_config']['share_id'] = share_id
            manager.save_form_data(form_id, form_data)
        else:
            share_id = form_data['customer_config']['share_id']

        # Create the shareable link
        share_link = request.host_url.rstrip('/') + url_for('customer_shared', share_id=share_id)

        return render_template('customer_share.html', form_data=form_data, form_id=form_id, share_link=share_link)

    @app.route('/customer/shared/<share_id>')
    def customer_shared(share_id):
        """View a shared customer form configuration"""
        # Find the form with this share ID
        manager = FormResponseManager()
        found_form = None

        for form_id in manager.storage.list_saved_forms():
            form_data = manager.load_form_data(form_id)
            if form_data and 'customer_config' in form_data and form_data['customer_config'].get('share_id') == share_id:
                found_form = form_data
                break

        if not found_form:
            flash('Shared configuration not found.', 'danger')
            return redirect(url_for('index'))

        return render_template('customer_shared.html', form_data=found_form)

    @app.route('/debug/form/<form_id>')
    def debug_form(form_id):
        """Debug route to inspect form structure"""
        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)

        if not form_data:
            return jsonify({'error': 'Form not found'}), 404

        # Extract just the essential info for debugging
        questions_info = []
        for q in form_data.get('questions', []):
            questions_info.append({
                'id': q.get('id'),
                'title': q.get('title'),
                'type': q.get('type'),
                'type_id': q.get('type_id', 'N/A'),
                'id_type': type(q.get('id')).__name__
            })

        return jsonify({
            'form_id': form_id,
            'form_title': form_data.get('form_title'),
            'questions_count': len(form_data.get('questions', [])),
            'questions': questions_info
        })

    @app.route('/debug/form/<form_id>/responses')
    def debug_form_responses(form_id):
        """Debug route to inspect form responses directly"""
        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)

        if not form_data:
            return jsonify({'error': 'Form not found'}), 404

        # Extract response data for debugging
        responses_info = {}
        for question in form_data.get('questions', []):
            q_id = question.get('id')
            q_title = question.get('title')

            # Count responses for this question
            q_responses = form_data.get('responses', {}).get(str(q_id), [])
            if not q_responses and q_id is not None:
                # Try with integer ID if string ID didn't work
                q_responses = form_data.get('responses', {}).get(q_id, [])

            responses_info[q_title] = {
                'question_id': q_id,
                'question_id_type': type(q_id).__name__,
                'response_count': len(q_responses),
                'responses': q_responses
            }

        # Get a dump of the actual responses object
        raw_responses = form_data.get('responses', {})
        response_keys = list(raw_responses.keys())
        response_key_types = [type(k).__name__ for k in response_keys]

        return jsonify({
            'form_id': form_id,
            'form_title': form_data.get('form_title'),
            'questions_count': len(form_data.get('questions', [])),
            'responses_object': raw_responses,
            'response_keys': response_keys,
            'response_key_types': response_key_types,
            'questions_with_responses': responses_info
        })

    # API Routes
    @app.route('/api/generate_for_question', methods=['POST'])
    def api_generate_for_question():
        """API endpoint for generating responses for a single question"""
        form_id = request.form.get('form_id')
        question_id = request.form.get('question_id')
        question_index = request.form.get('question_index')  # Optional: use index instead of ID
        method = request.form.get('method')

        if not all([form_id, method]) or (not question_id and not question_index):
            return jsonify({'error': 'Missing required parameters'}), 400

        # Load form data
        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)

        if not form_data:
            return jsonify({'error': 'Form not found'}), 404

        # Debug: print question_id type and value
        if question_id:
            print(f"Request question_id: {question_id} (type: {type(question_id).__name__})")
        if question_index:
            print(f"Request question_index: {question_index}")

        # Find question in form data
        question = None
        all_question_ids = []  # For debugging

        # Try to find by index first if provided
        if question_index and question_index.isdigit():
            # Frontend sends 1-based index (loop.index), convert to 0-based for array access
            idx = int(question_index) - 1  # Convert 1-based to 0-based index
            questions = form_data.get('questions', [])
            if 0 <= idx < len(questions):
                question = questions[idx]
                question_id = question.get('id')  # Set question_id for later use
                print(f"Found question by index {idx} (original: {question_index}): {question.get('title')}, ID: {question_id}")
            else:
                print(f"Invalid question index {question_index} (converted to {idx}), valid range: 0 to {len(questions)-1}")
        # Otherwise find by ID
        elif question_id:
            for q in form_data.get('questions', []):
                q_id = q.get('id')
                q_id_type = type(q_id).__name__
                all_question_ids.append({'id': q_id, 'type': q_id_type})

                # Try multiple comparison methods
                if q_id == question_id:  # Direct comparison
                    print(f"Found match with direct comparison: {q_id} == {question_id}")
                    question = q
                    break
                elif str(q_id) == question_id:  # String comparison
                    print(f"Found match with string comparison: str({q_id}) == {question_id}")
                    question = q
                    break
                elif q_id == int(question_id) if question_id.isdigit() else False:  # Integer comparison
                    print(f"Found match with int comparison: {q_id} == int({question_id})")
                    question = q
                    break

        if not question:
            print(f"Question not found. Available question IDs: {all_question_ids}")
            return jsonify({'error': 'Question not found', 'available_ids': all_question_ids}), 404

        # Process based on method
        if method == 'manual_checkbox':
            # Process checkbox data with multiple selections
            checkbox_data_str = request.form.get('checkbox_data')

            if not checkbox_data_str:
                return jsonify({'error': 'No checkbox data provided'}), 400

            try:
                # Parse the JSON data
                checkbox_data = json.loads(checkbox_data_str)

                if not checkbox_data or not isinstance(checkbox_data, list):
                    return jsonify({'error': 'Invalid checkbox data format'}), 400

                # Clear existing responses for this question
                if str(question_id) in form_data.get('responses', {}):
                    form_data['responses'][str(question_id)] = []
                    manager.save_form_data(form_id, form_data)

                # Add each selected option with its weight
                for option_data in checkbox_data:
                    option_text = option_data.get('text')
                    option_weight = option_data.get('weight', 1)

                    if option_text:
                        manager.add_response(form_id, question_id, option_text, option_weight)

                return jsonify({'success': True, 'message': f'Added {len(checkbox_data)} checkbox options for question {question["title"]}'})

            except json.JSONDecodeError:
                return jsonify({'error': 'Invalid JSON data for checkbox options'}), 400
            except Exception as e:
                return jsonify({'error': f'Error processing checkbox data: {str(e)}'}), 500

        elif method == 'manual_options':
            # Process options with weights
            options = question.get('options', [])

            # Check if options exist
            if not options:
                return jsonify({'error': 'This question has no options to process'}), 400

            # Get the count parameter for how many responses to generate
            count = request.form.get('count', '1')
            try:
                count = int(count)
                if count <= 0:
                    count = 1
            except ValueError:
                count = 1

            # Process each option with its weight
            responses_added = 0

            # First, collect all options and their weights
            option_weights = []
            total_weight = 0

            for i, option in enumerate(options):
                weight_key = f'weight_{i}'
                weight = request.form.get(weight_key)

                try:
                    weight = int(weight) if weight else 0
                except ValueError:
                    return jsonify({'error': f'Invalid weight for option {option}'}), 400

                if weight > 0:
                    option_weights.append((option, weight))
                    total_weight += weight

            # Clear existing responses for this question
            if str(question_id) in form_data.get('responses', {}):
                form_data['responses'][str(question_id)] = []
                manager.save_form_data(form_id, form_data)

            if total_weight == 0:
                return jsonify({'error': 'No options were selected (all weights are 0)'}), 400

            # Add each option with its weight (only once)
            for option, weight in option_weights:
                manager.add_response(form_id, question_id, option, weight)
                responses_added += 1

            return jsonify({'success': True, 'message': f'Added {responses_added} weighted option responses for question {question["title"]}'})

        elif method == 'manual_rating':
            # Process rating question with weights
            options = question.get('options', [])

            # Check if options exist
            if not options:
                return jsonify({'error': 'This rating question has no options to process'}), 400

            # Process each rating option with its weight
            responses_added = 0

            # First, collect all rating options and their weights
            option_weights = []
            total_weight = 0

            for i, option in enumerate(options):
                weight_key = f'weight_{i}'
                weight = request.form.get(weight_key)

                try:
                    weight = int(weight) if weight else 0
                except ValueError:
                    return jsonify({'error': f'Invalid weight for rating {option}'}), 400

                if weight > 0:
                    option_weights.append((option, weight))
                    total_weight += weight

            # Clear existing responses for this question
            if str(question_id) in form_data.get('responses', {}):
                form_data['responses'][str(question_id)] = []
                manager.save_form_data(form_id, form_data)

            if total_weight == 0:
                return jsonify({'error': 'No rating values were selected (all weights are 0)'}), 400

            # Add each rating option with its weight (only once)
            for option, weight in option_weights:
                manager.add_response(form_id, question_id, option, weight)
                responses_added += 1

            return jsonify({'success': True, 'message': f'Added {responses_added} weighted rating responses for question {question["title"]}'})

        elif method == 'manual_linear_scale':
            # Process linear scale question with weights
            options = question.get('options', [])

            # Check if options exist
            if not options:
                return jsonify({'error': 'This linear scale question has no options to process'}), 400

            # Process each scale option with its weight
            responses_added = 0

            # First, collect all scale options and their weights
            option_weights = []
            total_weight = 0

            for i, option in enumerate(options):
                weight_key = f'weight_{i}'
                weight = request.form.get(weight_key)

                try:
                    weight = int(weight) if weight else 0
                except ValueError:
                    return jsonify({'error': f'Invalid weight for scale {option}'}), 400

                if weight > 0:
                    option_weights.append((option, weight))
                    total_weight += weight

            # Clear existing responses for this question
            if str(question_id) in form_data.get('responses', {}):
                form_data['responses'][str(question_id)] = []
                manager.save_form_data(form_id, form_data)

            if total_weight == 0:
                return jsonify({'error': 'No scale values were selected (all weights are 0)'}), 400

            # Add each scale option with its weight (only once)
            for option, weight in option_weights:
                manager.add_response(form_id, question_id, option, weight)
                responses_added += 1

            return jsonify({'success': True, 'message': f'Added {responses_added} weighted linear scale responses for question {question["title"]}'})

        elif method == 'manual_checkbox_grid':
            # Process checkbox grid question with new data format
            grid_data_str = request.form.get('grid_data')

            if grid_data_str:
                # New format: process grid_data JSON
                try:
                    import json
                    grid_data = json.loads(grid_data_str)

                    if not grid_data:
                        return jsonify({'error': 'No grid combinations selected'}), 400

                    # Clear existing responses for this question
                    if str(question_id) in form_data.get('responses', {}):
                        form_data['responses'][str(question_id)] = []
                        manager.save_form_data(form_id, form_data)

                    responses_added = 0
                    for item in grid_data:
                        combination = item.get('combination')
                        weight = item.get('weight', 1)

                        if combination:
                            manager.add_response(form_id, question_id, combination, weight)
                            responses_added += 1

                    return jsonify({'success': True, 'message': f'Added {responses_added} checkbox grid responses for question {question["title"]}'})

                except json.JSONDecodeError:
                    return jsonify({'error': 'Invalid JSON data for grid combinations'}), 400
                except Exception as e:
                    return jsonify({'error': f'Error processing grid data: {str(e)}'}), 500
            else:
                # Fallback to old format for backward compatibility
                grid_rows = question.get('grid_rows', [])
                grid_columns = question.get('grid_columns', [])

                if not grid_rows or not grid_columns:
                    return jsonify({'error': 'This checkbox grid question has no rows or columns to process'}), 400

                # Clear existing responses for this question
                if str(question_id) in form_data.get('responses', {}):
                    form_data['responses'][str(question_id)] = []
                    manager.save_form_data(form_id, form_data)

                responses_added = 0
                # Process grid selections
                for row_idx, row in enumerate(grid_rows):
                    for col_idx, column in enumerate(grid_columns):
                        checkbox_name = f'grid_{row_idx}_{col_idx}'
                        if request.form.get(checkbox_name):
                            response_text = f'{row}: {column}'
                            manager.add_response(form_id, question_id, response_text, 1)
                            responses_added += 1

                return jsonify({'success': True, 'message': f'Added {responses_added} checkbox grid responses for question {question["title"]}'})

        elif method == 'manual_multiple_choice_grid':
            # Process multiple choice grid question with new data format
            multi_grid_data_str = request.form.get('multi_grid_data')

            if multi_grid_data_str:
                # New format: process multi_grid_data JSON
                try:
                    import json
                    multi_grid_data = json.loads(multi_grid_data_str)

                    if not multi_grid_data:
                        return jsonify({'error': 'No grid combinations selected'}), 400

                    # Clear existing responses for this question
                    if str(question_id) in form_data.get('responses', {}):
                        form_data['responses'][str(question_id)] = []
                        manager.save_form_data(form_id, form_data)

                    responses_added = 0
                    for item in multi_grid_data:
                        combination = item.get('combination')
                        weight = item.get('weight', 1)

                        if combination:
                            manager.add_response(form_id, question_id, combination, weight)
                            responses_added += 1

                    return jsonify({'success': True, 'message': f'Added {responses_added} multiple choice grid responses for question {question["title"]}'})

                except json.JSONDecodeError:
                    return jsonify({'error': 'Invalid JSON data for grid combinations'}), 400
                except Exception as e:
                    return jsonify({'error': f'Error processing grid data: {str(e)}'}), 500
            else:
                # Fallback to old format for backward compatibility
                grid_rows = question.get('grid_rows', [])
                grid_columns = question.get('grid_columns', [])

                if not grid_rows or not grid_columns:
                    return jsonify({'error': 'This multiple choice grid question has no rows or columns to process'}), 400

                # Clear existing responses for this question
                if str(question_id) in form_data.get('responses', {}):
                    form_data['responses'][str(question_id)] = []
                    manager.save_form_data(form_id, form_data)

                responses_added = 0
                # Process grid selections (one per row)
                for row_idx, row in enumerate(grid_rows):
                    radio_name = f'grid_row_{row_idx}'
                    selected_column = request.form.get(radio_name)
                    if selected_column:
                        response_text = f'{row}: {selected_column}'
                        manager.add_response(form_id, question_id, response_text, 1)
                        responses_added += 1

                return jsonify({'success': True, 'message': f'Added {responses_added} multiple choice grid responses for question {question["title"]}'})

        elif method == 'manual_text':
            # Process manual text responses
            responses_text = request.form.get('responses', '')
            add_weights = request.form.get('add_weights') == 'on'

            if not responses_text.strip():
                return jsonify({'error': 'No responses provided'}), 400

            responses = responses_text.strip().split('\n')
            weight_pattern = r'(.*)\s*\[(\d+)\]$'

            for response in responses:
                response = response.strip()
                if not response:
                    continue

                if add_weights:
                    match = re.match(weight_pattern, response)
                    if match:
                        text = match.group(1).strip()
                        weight = int(match.group(2))
                        manager.add_response(form_id, question_id, text, weight)
                    else:
                        manager.add_response(form_id, question_id, response, 1)
                else:
                    manager.add_response(form_id, question_id, response, 1)

            return jsonify({'success': True, 'message': f'Added manual responses for question {question["title"]}'})

        elif method in ['ai_assisted', 'fully_automated']:
            # Use AI provider to generate responses
            provider_name = request.form.get('provider', 'gemini')
            model = request.form.get('model', '')
            api_key = request.form.get('api_key', '')
            count = request.form.get('count', '5')
            prompt = request.form.get('prompt', '')
            prevent_duplicates = request.form.get('prevent_duplicates') == 'on'

            try:
                count = int(count)
            except ValueError:
                return jsonify({'error': 'Invalid count value'}), 400

            try:
                # Import provider manager and prompt utilities
                from ai_providers.provider_manager import ProviderManager
                from ai_providers.prompt_utils import (
                    create_open_ended_prompt,
                    parse_open_ended_responses,
                    assign_weightage
                )

                # Initialize provider manager
                provider_manager = ProviderManager()

                # Get the provider
                provider = provider_manager.get_provider(provider_name)

                if not provider:
                    return jsonify({'error': f'Provider {provider_name} not available'}), 400

                # Set model if specified
                if model:
                    provider.set_model(model)

                # Use specific API key if provided
                if api_key:
                    # Create a new provider instance with the provided key
                    from ai_providers.provider_factory import ProviderFactory
                    provider = ProviderFactory.create_provider(provider_name, api_key, model)

                if method == 'ai_assisted':
                    # Generate responses with custom prompt if provided
                    if prompt:
                        custom_prompt = f"Generate {count} different responses for the following question:\n\n"
                        custom_prompt += f"Question: {question['title']}\n\n"
                        custom_prompt += f"Additional instructions: {prompt}\n\n"
                        custom_prompt += f"Please provide {count} unique, thoughtful responses that a real person might give.\n"
                        custom_prompt += "Return your response as a JSON array where each element is a separate response.\n\n"
                        custom_prompt += "Format example:\n"
                        custom_prompt += "```json\n"
                        custom_prompt += '["Response 1", "Response 2", "Response 3"]\n'
                        custom_prompt += "```\n\n"
                        custom_prompt += "Make sure each response is complete, meaningful, and different from the others."

                        response_text = provider.generate_text(custom_prompt, max_tokens=2048)
                    else:
                        # Use standard prompt
                        custom_prompt = create_open_ended_prompt(question['title'], count)
                        response_text = provider.generate_text(custom_prompt, max_tokens=2048)

                    # Parse responses
                    responses = parse_open_ended_responses(response_text, count)

                    # Save responses with equal weight
                    for response in responses:
                        print(f"Adding response: '{response}' with weight 1 for question_id: {question_id} (type: {type(question_id).__name__})")
                        success = manager.add_response(form_id, question_id, response, 1)
                        print(f"Response added successfully: {success}")

                elif method == 'fully_automated':
                    # For fully automated, generate more responses and assign weights
                    if prompt:
                        custom_prompt = f"Generate {count} different responses for the following question:\n\n"
                        custom_prompt += f"Question: {question['title']}\n\n"
                        custom_prompt += f"Additional instructions: {prompt}\n\n"
                        custom_prompt += f"Please provide {count} unique, thoughtful responses that a real person might give.\n"
                        custom_prompt += "Return your response as a JSON array where each element is a separate response.\n\n"
                        custom_prompt += "Format example:\n"
                        custom_prompt += "```json\n"
                        custom_prompt += '["Response 1", "Response 2", "Response 3"]\n'
                        custom_prompt += "```\n\n"
                        custom_prompt += "Make sure each response is complete, meaningful, and different from the others."

                        response_text = provider.generate_text(custom_prompt, max_tokens=2048)
                    else:
                        # Use standard prompt
                        custom_prompt = create_open_ended_prompt(question['title'], count)
                        response_text = provider.generate_text(custom_prompt, max_tokens=2048)

                    # Parse responses
                    responses = parse_open_ended_responses(response_text, count)

                    # Remove duplicates if requested
                    if prevent_duplicates:
                        responses = list(dict.fromkeys(responses))

                    # Assign weights
                    weighted_responses = assign_weightage(responses)

                    # Save each response with its weight
                    for response, weight in weighted_responses.items():
                        print(f"Adding weighted response: '{response}' with weight {weight} for question_id: {question_id} (type: {type(question_id).__name__})")
                        success = manager.add_response(form_id, question_id, response, weight)
                        print(f"Response added successfully: {success}")

                return jsonify({
                    'success': True,
                    'message': f'Generated {len(responses)} responses for question "{question["title"]}"',
                    'question_id': question_id
                })

            except Exception as e:
                import traceback
                traceback.print_exc()
                return jsonify({'error': f'Error generating responses: {str(e)}'}), 500

        else:
            return jsonify({'error': f'Unknown method: {method}'}), 400

    @app.route('/api/form/<form_id>', methods=['DELETE'])
    def api_delete_form_route(form_id):
        """API endpoint for deleting a form"""
        manager = FormResponseManager()
        if manager.delete_form(form_id):
            return jsonify({'success': True, 'message': 'Form deleted successfully'})
        else:
            return jsonify({'error': 'Failed to delete form or form not found'}), 404

    @app.route('/api/add_response', methods=['POST'])
    def api_add_response_route():
        """API endpoint for adding a response"""
        data = request.get_json()
        form_id = request.args.get('form_id')
        question_id = request.args.get('question_id')

        if not all([form_id, question_id]) or not data:
            return jsonify({'error': 'Missing required parameters'}), 400

        text = data.get('text')
        weight = data.get('weight', 1)

        if not text:
            return jsonify({'error': 'Text is required'}), 400

        manager = FormResponseManager()
        manager.add_response(form_id, question_id, text, weight)

        return jsonify({'success': True, 'message': 'Response added successfully'})

    @app.route('/api/delete_response/<form_id>/<question_id>/<int:response_index>', methods=['DELETE'])
    def api_delete_response_route(form_id, question_id, response_index):
        """API endpoint for deleting a response"""
        manager = FormResponseManager()
        success = manager.delete_response(form_id, question_id, response_index)

        if success:
            return jsonify({'success': True, 'message': 'Response deleted successfully'})
        else:
            return jsonify({'error': 'Failed to delete response or response not found'}), 404

    @app.route('/api/generate_by_title', methods=['POST'])
    def api_generate_by_title():
        """API endpoint for generating responses for a question found by title"""
        form_id = request.form.get('form_id')
        question_title = request.form.get('question_title')
        method = request.form.get('method')

        if not all([form_id, question_title, method]):
            return jsonify({'error': 'Missing required parameters'}), 400

        # Load form data
        manager = FormResponseManager()
        form_data = manager.load_form_data(form_id)

        if not form_data:
            return jsonify({'error': 'Form not found'}), 404

        # Debug info
        print(f"Searching for question with title: {question_title}")

        # Find question in form data by matching title (case insensitive)
        question = None
        question_id = None
        all_titles = []

        for i, q in enumerate(form_data.get('questions', [])):
            q_title = q.get('title', '')
            all_titles.append(q_title)

            # Try exact match and case-insensitive match
            if q_title == question_title or q_title.lower() == question_title.lower():
                question = q
                question_id = q.get('id')
                print(f"Found matching question at index {i}: {q_title}, ID: {question_id}")
                break

        if not question or not question_id:
            # If no exact match, try partial match
            for i, q in enumerate(form_data.get('questions', [])):
                q_title = q.get('title', '')
                if question_title.lower() in q_title.lower():
                    question = q
                    question_id = q.get('id')
                    print(f"Found partial match at index {i}: {q_title}, ID: {question_id}")
                    break

        if not question or not question_id:
            print(f"No matching question found. Available titles: {all_titles}")
            return jsonify({'error': 'Question not found', 'available_titles': all_titles}), 404

        # From here on, process the same way as the original API
        provider_name = request.form.get('provider', 'gemini')
        model = request.form.get('model', '')
        api_key = request.form.get('api_key', '')
        count = request.form.get('count', '5')
        prompt = request.form.get('prompt', '')
        prevent_duplicates = request.form.get('prevent_duplicates') == 'on'

        try:
            count = int(count)
        except ValueError:
            return jsonify({'error': 'Invalid count value'}), 400

        try:
            # Import provider manager and prompt utilities
            from ai_providers.provider_manager import ProviderManager
            from ai_providers.prompt_utils import (
                create_open_ended_prompt,
                parse_open_ended_responses,
                assign_weightage
            )

            # Initialize provider manager
            provider_manager = ProviderManager()

            # Get the provider
            provider = provider_manager.get_provider(provider_name)

            if not provider:
                return jsonify({'error': f'Provider {provider_name} not available'}), 400

            # Set model if specified
            if model:
                provider.set_model(model)

            # Use specific API key if provided
            if api_key:
                # Create a new provider instance with the provided key
                from ai_providers.provider_factory import ProviderFactory
                provider = ProviderFactory.create_provider(provider_name, api_key, model)

            if method == 'ai_assisted':
                # Generate responses with custom prompt if provided
                if prompt:
                    custom_prompt = f"Generate {count} different responses for the following question:\n\n"
                    custom_prompt += f"Question: {question['title']}\n\n"
                    custom_prompt += f"Additional instructions: {prompt}\n\n"
                    custom_prompt += f"Please provide {count} unique, thoughtful responses that a real person might give.\n"
                    custom_prompt += "Return your response as a JSON array where each element is a separate response.\n\n"
                    custom_prompt += "Format example:\n"
                    custom_prompt += "```json\n"
                    custom_prompt += '["Response 1", "Response 2", "Response 3"]\n'
                    custom_prompt += "```\n\n"
                    custom_prompt += "Make sure each response is complete, meaningful, and different from the others."

                    response_text = provider.generate_text(custom_prompt, max_tokens=2048)
                else:
                    # Use standard prompt
                    custom_prompt = create_open_ended_prompt(question['title'], count)
                    response_text = provider.generate_text(custom_prompt, max_tokens=2048)

                # Parse responses
                responses = parse_open_ended_responses(response_text, count)

                # Save responses with equal weight
                for response in responses:
                    print(f"Adding response: '{response}' with weight 1 for question_id: {question_id} (type: {type(question_id).__name__})")
                    success = manager.add_response(form_id, question_id, response, 1)
                    print(f"Response added successfully: {success}")

            elif method == 'fully_automated':
                # For fully automated, generate more responses and assign weights
                if prompt:
                    custom_prompt = f"Generate {count} different responses for the following question:\n\n"
                    custom_prompt += f"Question: {question['title']}\n\n"
                    custom_prompt += f"Additional instructions: {prompt}\n\n"
                    custom_prompt += f"Please provide {count} unique, thoughtful responses that a real person might give.\n"
                    custom_prompt += "Return your response as a JSON array where each element is a separate response.\n\n"
                    custom_prompt += "Format example:\n"
                    custom_prompt += "```json\n"
                    custom_prompt += '["Response 1", "Response 2", "Response 3"]\n'
                    custom_prompt += "```\n\n"
                    custom_prompt += "Make sure each response is complete, meaningful, and different from the others."

                    response_text = provider.generate_text(custom_prompt, max_tokens=2048)
                else:
                    # Use standard prompt
                    custom_prompt = create_open_ended_prompt(question['title'], count)
                    response_text = provider.generate_text(custom_prompt, max_tokens=2048)

                # Parse responses
                responses = parse_open_ended_responses(response_text, count)

                # Remove duplicates if requested
                if prevent_duplicates:
                    responses = list(dict.fromkeys(responses))

                # Assign weights
                weighted_responses = assign_weightage(responses)

                # Save each response with its weight
                for response, weight in weighted_responses.items():
                    print(f"Adding weighted response: '{response}' with weight {weight} for question_id: {question_id} (type: {type(question_id).__name__})")
                    success = manager.add_response(form_id, question_id, response, weight)
                    print(f"Response added successfully: {success}")

            return jsonify({
                'success': True,
                'message': f'Generated {len(responses)} responses for question "{question["title"]}"',
                'question_id': question_id
            })

        except Exception as e:
            import traceback
            traceback.print_exc()
            return jsonify({'error': f'Error generating responses: {str(e)}'}), 500

    @app.route('/api/providers', methods=['GET'])
    def api_get_providers():
        """API endpoint for getting available AI providers"""
        try:
            # Import provider manager
            from ai_providers.provider_manager import ProviderManager

            # Initialize provider manager
            provider_manager = ProviderManager()

            # Get available providers
            providers = provider_manager.get_available_providers()

            return jsonify({
                'success': True,
                'providers': providers
            })
        except Exception as e:
            return jsonify({'error': f'Error getting providers: {str(e)}'}), 500

    @app.route('/api/models/<provider>', methods=['GET'])
    def api_get_models(provider):
        """API endpoint for getting available models for a provider"""
        try:
            # Import provider manager
            from ai_providers.provider_manager import ProviderManager

            # Initialize provider manager
            provider_manager = ProviderManager()

            # Get available models
            models = provider_manager.get_available_models(provider)

            return jsonify({
                'success': True,
                'models': models.get(provider, [])
            })
        except Exception as e:
            return jsonify({'error': f'Error getting models: {str(e)}'}), 500

    @app.route('/api/openrouter/models', methods=['GET'])
    def api_get_openrouter_models():
        """API endpoint specifically for fetching OpenRouter models with search support"""
        try:
            from ai_providers.openrouter_provider import OpenRouterProvider
            from ai_providers.provider_manager import ProviderManager

            # Get search query parameter
            search_query = request.args.get('search', '').lower()

            # Initialize provider manager to get configured keys
            provider_manager = ProviderManager()
            provider = provider_manager.get_provider('openrouter')

            if not provider:
                # If no configured provider, try with a dummy key to get models list
                # (OpenRouter models endpoint works without auth for basic model list)
                try:
                    import requests
                    response = requests.get("https://openrouter.ai/api/v1/models", timeout=10)
                    response.raise_for_status()
                    result = response.json()

                    models = []
                    for model in result.get("data", []):
                        models.append({
                            "id": model.get("id"),
                            "name": model.get("name", model.get("id", "")),
                            "description": model.get("description", ""),
                            "context_length": model.get("context_length", 0),
                            "pricing": model.get("pricing", {}),
                            "input_modalities": model.get("architecture", {}).get("input_modalities", []),
                            "output_modalities": model.get("architecture", {}).get("output_modalities", [])
                        })
                except Exception as e:
                    return jsonify({'error': f'Could not fetch models: {str(e)}'}), 500
            else:
                # Use configured provider to get models
                models = provider.get_available_models()

            # Filter models based on search query if provided
            if search_query:
                filtered_models = []
                for model in models:
                    model_text = f"{model.get('id', '')} {model.get('name', '')} {model.get('description', '')}".lower()
                    if search_query in model_text:
                        filtered_models.append(model)
                models = filtered_models

            # Format for Select2 dropdown
            formatted_models = []
            for model in models:
                # Create display text with model name and description
                display_text = model.get('name', model.get('id', ''))
                if model.get('description'):
                    display_text += f" - {model.get('description')[:100]}..."

                # Add context length and pricing info if available
                context_info = []
                if model.get('context_length'):
                    context_info.append(f"Context: {model.get('context_length'):,}")

                pricing = model.get('pricing', {})
                if pricing.get('prompt'):
                    context_info.append(f"${pricing.get('prompt')}/1K tokens")

                if context_info:
                    display_text += f" ({', '.join(context_info)})"

                formatted_models.append({
                    'id': model.get('id'),
                    'text': display_text,
                    'name': model.get('name', ''),
                    'description': model.get('description', ''),
                    'context_length': model.get('context_length', 0),
                    'pricing': pricing,
                    'input_modalities': model.get('input_modalities', []),
                    'output_modalities': model.get('output_modalities', [])
                })

            return jsonify({
                'success': True,
                'models': formatted_models,
                'total': len(formatted_models)
            })

        except Exception as e:
            return jsonify({'error': f'Error fetching OpenRouter models: {str(e)}'}), 500

    @app.route('/api/generate/batch', methods=['POST'])
    def api_generate_batch():
        """API endpoint for batch generating responses for multiple questions"""
        try:
            # Get form data
            form_id = request.form.get('form_id')
            provider_name = request.form.get('provider', 'gemini')
            model = request.form.get('model', '')
            api_key = request.form.get('api_key', '')
            sample_count = request.form.get('sample_count', '5')
            method = request.form.get('method', 'batch_optimized')

            try:
                sample_count = int(sample_count)
            except ValueError:
                return jsonify({'error': 'Invalid sample count value'}), 400

            # Initialize manager
            manager = FormResponseManager()

            # Get form data
            form_data = manager.load_form_data(form_id)
            if not form_data:
                return jsonify({'error': 'Form not found'}), 404

            # Get questions
            questions = form_data.get('questions', [])
            if not questions:
                return jsonify({'error': 'No questions found in form'}), 400

            # Import provider manager and prompt utilities
            from ai_providers.provider_manager import ProviderManager
            from ai_providers.prompt_utils import (
                create_batch_prompt,
                parse_batch_responses,
                assign_weightage
            )

            # Initialize provider manager
            provider_manager = ProviderManager()

            # Get the provider
            provider = provider_manager.get_provider(provider_name)

            if not provider:
                return jsonify({'error': f'Provider {provider_name} not available'}), 400

            # Set model if specified
            if model:
                provider.set_model(model)

            # Use specific API key if provided
            if api_key:
                # Create a new provider instance with the provided key
                from ai_providers.provider_factory import ProviderFactory
                provider = ProviderFactory.create_provider(provider_name, api_key, model)

            # Create batch prompt
            batch_prompt = create_batch_prompt(questions, sample_count)

            # Generate responses
            response_text = provider.generate_text(batch_prompt, max_tokens=4096)

            # Parse responses
            responses_by_question = parse_batch_responses(response_text, questions)

            # Process and save responses
            results = []
            for question in questions:
                question_id = question['id']
                question_responses = responses_by_question.get(question_id, [])

                if question_responses:
                    # Assign weights
                    weighted_responses = assign_weightage(question_responses)

                    # Save each response with its weight
                    for response, weight in weighted_responses.items():
                        success = manager.add_response(form_id, question_id, response, weight)

                    results.append({
                        'question_id': question_id,
                        'question_title': question.get('title', ''),
                        'responses_count': len(question_responses)
                    })

            return jsonify({
                'success': True,
                'message': f'Generated responses for {len(results)} questions',
                'results': results
            })
        except Exception as e:
            import traceback
            traceback.print_exc()
            return jsonify({'error': f'Error generating batch responses: {str(e)}'}), 500

